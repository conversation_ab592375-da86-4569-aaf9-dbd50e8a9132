using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    public class Sale : INotifyPropertyChanged
    {
        private int _id;
        private string _invoiceNumber;
        private int? _customerId;
        private string _customerName;
        private DateTime _date;
        private decimal _subtotal;
        private decimal _discount;
        private decimal _tax;
        private decimal _total;
        private string _paymentMethod; // Cash, Credit Card, Bank Transfer, etc.
        private string _paymentStatus; // Paid, Unpaid, Partial
        private string _notes;
        private DateTime _createdAt;
        private DateTime? _updatedAt;
        private ObservableCollection<SaleItem> _items;

        public Sale()
        {
            _items = new ObservableCollection<SaleItem>();
            _date = DateTime.Now;
            _createdAt = DateTime.Now;
            _paymentMethod = "Cash";
            _paymentStatus = "Paid";
        }

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set
            {
                if (_invoiceNumber != value)
                {
                    _invoiceNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CustomerName
        {
            get => _customerName;
            set
            {
                if (_customerName != value)
                {
                    _customerName = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime Date
        {
            get => _date;
            set
            {
                if (_date != value)
                {
                    _date = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Subtotal
        {
            get => _subtotal;
            set
            {
                if (_subtotal != value)
                {
                    _subtotal = value;
                    OnPropertyChanged();
                    CalculateTotal();
                }
            }
        }

        public decimal Discount
        {
            get => _discount;
            set
            {
                if (_discount != value)
                {
                    _discount = value;
                    OnPropertyChanged();
                    CalculateTotal();
                }
            }
        }

        public decimal Tax
        {
            get => _tax;
            set
            {
                if (_tax != value)
                {
                    _tax = value;
                    OnPropertyChanged();
                    CalculateTotal();
                }
            }
        }

        public decimal Total
        {
            get => _total;
            set
            {
                if (_total != value)
                {
                    _total = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PaymentMethod
        {
            get => _paymentMethod;
            set
            {
                if (_paymentMethod != value)
                {
                    _paymentMethod = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PaymentStatus
        {
            get => _paymentStatus;
            set
            {
                if (_paymentStatus != value)
                {
                    _paymentStatus = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsPaid));
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<SaleItem> Items
        {
            get => _items;
            set
            {
                if (_items != value)
                {
                    _items = value;
                    OnPropertyChanged();
                }
            }
        }

        // Calculated properties
        public bool IsPaid => PaymentStatus?.ToLower() == "paid";

        private void CalculateTotal()
        {
            Total = Subtotal - Discount + Tax;
        }

        public void CalculateSubtotal()
        {
            decimal subtotal = 0;
            foreach (var item in Items)
            {
                subtotal += item.Total;
            }
            Subtotal = subtotal;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class SaleItem : INotifyPropertyChanged
    {
        private int _id;
        private int _saleId;
        private int _productId;
        private string _productName;
        private string _productCode;
        private int _quantity;
        private decimal _unitPrice;
        private decimal _discount;
        private decimal _total;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int SaleId
        {
            get => _saleId;
            set
            {
                if (_saleId != value)
                {
                    _saleId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ProductId
        {
            get => _productId;
            set
            {
                if (_productId != value)
                {
                    _productId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProductName
        {
            get => _productName;
            set
            {
                if (_productName != value)
                {
                    _productName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProductCode
        {
            get => _productCode;
            set
            {
                if (_productCode != value)
                {
                    _productCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public int Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged();
                    CalculateTotal();
                }
            }
        }

        public decimal UnitPrice
        {
            get => _unitPrice;
            set
            {
                if (_unitPrice != value)
                {
                    _unitPrice = value;
                    OnPropertyChanged();
                    CalculateTotal();
                }
            }
        }

        public decimal Discount
        {
            get => _discount;
            set
            {
                if (_discount != value)
                {
                    _discount = value;
                    OnPropertyChanged();
                    CalculateTotal();
                }
            }
        }

        public decimal Total
        {
            get => _total;
            set
            {
                if (_total != value)
                {
                    _total = value;
                    OnPropertyChanged();
                }
            }
        }

        private void CalculateTotal()
        {
            Total = (Quantity * UnitPrice) - Discount;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class SaleValidator : AbstractValidator<Sale>
    {
        public SaleValidator()
        {
            RuleFor(s => s.InvoiceNumber).NotEmpty().WithMessage("Invoice number is required")
                .MaximumLength(20).WithMessage("Invoice number cannot exceed 20 characters");

            RuleFor(s => s.Date).NotEmpty().WithMessage("Date is required")
                .LessThanOrEqualTo(DateTime.Now).WithMessage("Date cannot be in the future");

            RuleFor(s => s.Subtotal).GreaterThanOrEqualTo(0).WithMessage("Subtotal must be greater than or equal to 0");

            RuleFor(s => s.Discount).GreaterThanOrEqualTo(0).WithMessage("Discount must be greater than or equal to 0");

            RuleFor(s => s.Tax).GreaterThanOrEqualTo(0).WithMessage("Tax must be greater than or equal to 0");

            RuleFor(s => s.Total).GreaterThan(0).WithMessage("Total must be greater than 0");

            RuleFor(s => s.PaymentMethod).NotEmpty().WithMessage("Payment method is required")
                .Must(pm => new[] { "cash", "credit card", "bank transfer", "check" }.Contains(pm.ToLower()))
                .WithMessage("Payment method must be one of: Cash, Credit Card, Bank Transfer, Check");

            RuleFor(s => s.PaymentStatus).NotEmpty().WithMessage("Payment status is required")
                .Must(ps => new[] { "paid", "unpaid", "partial" }.Contains(ps.ToLower()))
                .WithMessage("Payment status must be one of: Paid, Unpaid, Partial");

            RuleFor(s => s.Items).NotEmpty().WithMessage("Sale must have at least one item");
        }
    }

    public class SaleItemValidator : AbstractValidator<SaleItem>
    {
        public SaleItemValidator()
        {
            RuleFor(si => si.ProductId).NotEmpty().WithMessage("Product is required");

            RuleFor(si => si.Quantity).GreaterThan(0).WithMessage("Quantity must be greater than 0");

            RuleFor(si => si.UnitPrice).GreaterThan(0).WithMessage("Unit price must be greater than 0");

            RuleFor(si => si.Discount).GreaterThanOrEqualTo(0).WithMessage("Discount must be greater than or equal to 0");

            RuleFor(si => si.Total).GreaterThan(0).WithMessage("Total must be greater than 0");
        }
    }
}