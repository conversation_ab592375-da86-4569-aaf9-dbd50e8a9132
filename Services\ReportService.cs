using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class ReportService
    {
        private readonly DatabaseService _dbService;
        private readonly SaleService _saleService;
        private readonly PurchaseService _purchaseService;
        private readonly ProductService _productService;
        private readonly CustomerService _customerService;
        private readonly SupplierService _supplierService;
        private readonly EmployeeService _employeeService;
        private readonly ExpenseService _expenseService;

        public ReportService(
            DatabaseService dbService,
            SaleService saleService,
            PurchaseService purchaseService,
            ProductService productService,
            CustomerService customerService,
            SupplierService supplierService,
            EmployeeService employeeService,
            ExpenseService expenseService)
        {
            _dbService = dbService;
            _saleService = saleService;
            _purchaseService = purchaseService;
            _productService = productService;
            _customerService = customerService;
            _supplierService = supplierService;
            _employeeService = employeeService;
            _expenseService = expenseService;
        }

        #region Sales Reports

        public async Task<SalesReport> GetSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            var report = new SalesReport
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalSales = await _saleService.GetTotalSalesAsync(startDate, endDate),
                SalesByMonth = (await _saleService.GetSaleSummaryByMonthAsync(DateTime.Now.Year)).ToList(),
                TopProducts = (await _saleService.GetSaleSummaryByProductAsync(startDate, endDate)).Take(10).ToList(),
                TopCustomers = (await _customerService.GetTopCustomersAsync(10)).ToList()
            };

            return report;
        }

        public async Task<IEnumerable<SaleSummaryByMonth>> GetMonthlySalesReportAsync(int year)
        {
            return await _saleService.GetSaleSummaryByMonthAsync(year);
        }

        public async Task<IEnumerable<SaleSummaryByProduct>> GetProductSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            return await _saleService.GetSaleSummaryByProductAsync(startDate, endDate);
        }

        #endregion

        #region Purchase Reports

        public async Task<PurchaseReport> GetPurchaseReportAsync(DateTime startDate, DateTime endDate)
        {
            var report = new PurchaseReport
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalPurchases = await _purchaseService.GetTotalPurchasesAsync(startDate, endDate),
                PurchasesByMonth = (await _purchaseService.GetPurchaseSummaryByMonthAsync(DateTime.Now.Year)).ToList(),
                TopProducts = (await _purchaseService.GetPurchaseSummaryByProductAsync(startDate, endDate)).Take(10).ToList(),
                TopSuppliers = (await _supplierService.GetTopSuppliersAsync(10)).ToList()
            };

            return report;
        }

        public async Task<IEnumerable<PurchaseSummaryByMonth>> GetMonthlyPurchaseReportAsync(int year)
        {
            return await _purchaseService.GetPurchaseSummaryByMonthAsync(year);
        }

        public async Task<IEnumerable<PurchaseSummaryByProduct>> GetProductPurchaseReportAsync(DateTime startDate, DateTime endDate)
        {
            return await _purchaseService.GetPurchaseSummaryByProductAsync(startDate, endDate);
        }

        #endregion

        #region Inventory Reports

        public async Task<InventoryReport> GetInventoryReportAsync()
        {
            var report = new InventoryReport
            {
                TotalProducts = await _productService.GetProductCountAsync(),
                TotalStockValue = await _productService.GetTotalStockValueAsync(),
                LowStockProducts = (await _productService.GetLowStockProductsAsync()).ToList(),
                ProductsByCategory = (await _productService.GetProductCountByCategoryAsync()).Select(c => new CategoryCount
                {
                    CategoryName = c.CategoryName,
                    ProductCount = c.ProductCount
                }).ToList()
            };

            return report;
        }

        public async Task<IEnumerable<Product>> GetLowStockReportAsync()
        {
            return await _productService.GetLowStockProductsAsync();
        }

        #endregion

        #region Financial Reports

        public async Task<FinancialReport> GetFinancialReportAsync(DateTime startDate, DateTime endDate)
        {
            decimal totalSales = await _saleService.GetTotalSalesAsync(startDate, endDate);
            decimal totalPurchases = await _purchaseService.GetTotalPurchasesAsync(startDate, endDate);
            decimal totalExpenses = await _expenseService.GetTotalExpensesAsync(startDate, endDate);
            decimal totalSalaries = await _employeeService.GetTotalSalariesAsync();

            var report = new FinancialReport
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalRevenue = totalSales,
                TotalCostOfGoods = totalPurchases,
                TotalExpenses = totalExpenses + totalSalaries,
                GrossProfit = totalSales - totalPurchases,
                NetProfit = totalSales - totalPurchases - totalExpenses - totalSalaries,
                ExpensesByCategory = (await _expenseService.GetExpenseSummaryByCategoryAsync(startDate, endDate)).ToList(),
                MonthlyProfitSummary = await GetMonthlyProfitSummaryAsync(DateTime.Now.Year)
            };

            return report;
        }

        public async Task<IEnumerable<MonthlyProfitSummary>> GetMonthlyProfitSummaryAsync(int year)
        {
            var salesByMonth = await _saleService.GetSaleSummaryByMonthAsync(year);
            var purchasesByMonth = await _purchaseService.GetPurchaseSummaryByMonthAsync(year);
            var expensesByMonth = await _expenseService.GetExpenseSummaryByMonthAsync(year);

            var result = new List<MonthlyProfitSummary>();

            for (int month = 1; month <= 12; month++)
            {
                string monthStr = month.ToString("D2");
                var salesForMonth = salesByMonth.FirstOrDefault(s => s.Month == monthStr);
                var purchasesForMonth = purchasesByMonth.FirstOrDefault(p => p.Month == monthStr);
                var expensesForMonth = expensesByMonth.FirstOrDefault(e => e.Month == monthStr);

                decimal salesAmount = salesForMonth?.TotalAmount ?? 0;
                decimal purchasesAmount = purchasesForMonth?.TotalAmount ?? 0;
                decimal expensesAmount = expensesForMonth?.TotalAmount ?? 0;

                result.Add(new MonthlyProfitSummary
                {
                    Month = monthStr,
                    MonthName = new DateTime(2000, month, 1).ToString("MMMM"),
                    Revenue = salesAmount,
                    CostOfGoods = purchasesAmount,
                    Expenses = expensesAmount,
                    GrossProfit = salesAmount - purchasesAmount,
                    NetProfit = salesAmount - purchasesAmount - expensesAmount
                });
            }

            return result;
        }

        public async Task<CashFlowReport> GetCashFlowReportAsync(DateTime startDate, DateTime endDate)
        {
            // Get all sales with payment method and status
            const string salesSql = @"
                SELECT Date, PaymentMethod, PaymentStatus, Total
                FROM Sales
                WHERE Date BETWEEN @StartDate AND @EndDate";

            var sales = await _dbService.QueryAsync<dynamic>(salesSql, new
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            });

            // Get all purchases with payment method and status
            const string purchasesSql = @"
                SELECT Date, PaymentMethod, PaymentStatus, Total
                FROM Purchases
                WHERE Date BETWEEN @StartDate AND @EndDate";

            var purchases = await _dbService.QueryAsync<dynamic>(purchasesSql, new
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            });

            // Get all expenses
            var expenses = await _expenseService.GetExpensesByDateRangeAsync(startDate, endDate);

            // Calculate cash inflows and outflows by payment method
            var cashInflows = sales
                .Where(s => s.PaymentStatus == "Paid")
                .GroupBy(s => s.PaymentMethod)
                .Select(g => new CashFlowItem
                {
                    Category = "Sales",
                    PaymentMethod = g.Key,
                    Amount = g.Sum(s => (decimal)s.Total)
                })
                .ToList();

            var cashOutflows = purchases
                .Where(p => p.PaymentStatus == "Paid")
                .GroupBy(p => p.PaymentMethod)
                .Select(g => new CashFlowItem
                {
                    Category = "Purchases",
                    PaymentMethod = g.Key,
                    Amount = g.Sum(p => (decimal)p.Total)
                })
                .ToList();

            // Add expenses to cash outflows
            cashOutflows.Add(new CashFlowItem
            {
                Category = "Expenses",
                PaymentMethod = "Various",
                Amount = expenses.Sum(e => e.Amount)
            });

            return new CashFlowReport
            {
                StartDate = startDate,
                EndDate = endDate,
                CashInflows = cashInflows,
                CashOutflows = cashOutflows,
                NetCashFlow = cashInflows.Sum(i => i.Amount) - cashOutflows.Sum(o => o.Amount)
            };
        }

        #endregion

        #region Customer Reports

        public async Task<CustomerReport> GetCustomerReportAsync()
        {
            var report = new CustomerReport
            {
                TotalCustomers = await _customerService.GetCustomerCountAsync(),
                TotalOutstandingBalance = await _customerService.GetTotalCustomerBalanceAsync(),
                CustomersWithOutstandingBalance = (await _customerService.GetCustomersWithOutstandingBalanceAsync()).ToList(),
                TopCustomers = (await _customerService.GetTopCustomersAsync(10)).ToList()
            };

            return report;
        }

        #endregion

        #region Supplier Reports

        public async Task<SupplierReport> GetSupplierReportAsync()
        {
            var report = new SupplierReport
            {
                TotalSuppliers = await _supplierService.GetAllSuppliersAsync().ContinueWith(t => t.Result.Count()),
                TotalOutstandingBalance = await _supplierService.GetTotalSupplierBalanceAsync(),
                SuppliersWithOutstandingBalance = (await _supplierService.GetSuppliersWithOutstandingBalanceAsync()).ToList(),
                TopSuppliers = (await _supplierService.GetTopSuppliersAsync(10)).ToList()
            };

            return report;
        }

        #endregion

        #region Employee Reports

        public async Task<EmployeeReport> GetEmployeeReportAsync()
        {
            var report = new EmployeeReport
            {
                TotalEmployees = await _employeeService.GetEmployeeCountAsync(),
                ActiveEmployees = await _employeeService.GetActiveEmployeeCountAsync(),
                TotalSalaries = await _employeeService.GetTotalSalariesAsync(),
                EmployeesByPosition = (await _employeeService.GetEmployeeCountByPositionAsync()).Select(e => new PositionCount
                {
                    Position = e.Position,
                    Count = e.EmployeeCount
                })
            };

            return report;
        }

        #endregion

        #region Dashboard

        public async Task<DashboardData> GetDashboardDataAsync()
        {
            // Get current month data
            DateTime startDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            DateTime endDate = startDate.AddMonths(1).AddDays(-1);

            // Get previous month data for comparison
            DateTime prevStartDate = startDate.AddMonths(-1);
            DateTime prevEndDate = startDate.AddDays(-1);

            decimal currentSales = await _saleService.GetTotalSalesAsync(startDate, endDate);
            decimal previousSales = await _saleService.GetTotalSalesAsync(prevStartDate, prevEndDate);

            decimal currentPurchases = await _purchaseService.GetTotalPurchasesAsync(startDate, endDate);
            decimal previousPurchases = await _purchaseService.GetTotalPurchasesAsync(prevStartDate, prevEndDate);

            decimal currentExpenses = await _expenseService.GetTotalExpensesAsync(startDate, endDate);
            decimal previousExpenses = await _expenseService.GetTotalExpensesAsync(prevStartDate, prevEndDate);

            decimal currentProfit = currentSales - currentPurchases - currentExpenses;
            decimal previousProfit = previousSales - previousPurchases - previousExpenses;

            var dashboard = new DashboardData
            {
                TodaySales = await _saleService.GetTotalSalesAsync(DateTime.Today, DateTime.Today),
                CurrentMonthSales = currentSales,
                SalesGrowth = CalculateGrowthPercentage(previousSales, currentSales),

                TodayPurchases = await _purchaseService.GetTotalPurchasesAsync(DateTime.Today, DateTime.Today),
                CurrentMonthPurchases = currentPurchases,
                PurchasesGrowth = CalculateGrowthPercentage(previousPurchases, currentPurchases),

                TodayExpenses = await _expenseService.GetTotalExpensesAsync(DateTime.Today, DateTime.Today),
                CurrentMonthExpenses = currentExpenses,
                ExpensesGrowth = CalculateGrowthPercentage(previousExpenses, currentExpenses),

                CurrentMonthProfit = currentProfit,
                ProfitGrowth = CalculateGrowthPercentage(previousProfit, currentProfit),

                LowStockProducts = (await _productService.GetLowStockProductsAsync()).Take(5).ToList(),
                RecentSales = (await _saleService.GetSalesByDateRangeAsync(DateTime.Today.AddDays(-7), DateTime.Today)).Take(5).ToList(),
                TopSellingProducts = (await _saleService.GetSaleSummaryByProductAsync(startDate, endDate)).Take(5).ToList(),
                CustomersWithOutstandingBalance = (await _customerService.GetCustomersWithOutstandingBalanceAsync()).Take(5).ToList()
            };

            return dashboard;
        }

        private decimal CalculateGrowthPercentage(decimal previous, decimal current)
        {
            if (previous == 0)
            {
                return current > 0 ? 100 : 0;
            }

            return Math.Round(((current - previous) / previous) * 100, 2);
        }

        #endregion
    }

    #region Report Models

    public class SalesReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalSales { get; set; }
        public List<SaleSummaryByMonth> SalesByMonth { get; set; } = new();
        public List<SaleSummaryByProduct> TopProducts { get; set; } = new();
        public List<Customer> TopCustomers { get; set; } = new();
    }

    public class PurchaseReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalPurchases { get; set; }
        public List<PurchaseSummaryByMonth> PurchasesByMonth { get; set; } = new();
        public List<PurchaseSummaryByProduct> TopProducts { get; set; } = new();
        public List<Supplier> TopSuppliers { get; set; } = new();
    }

    public class InventoryReport
    {
        public int TotalProducts { get; set; }
        public decimal TotalStockValue { get; set; }
        public List<Product> LowStockProducts { get; set; } = new();
        public List<CategoryCount> ProductsByCategory { get; set; } = new();
    }

    public class CategoryCount
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public int ProductCount { get; set; }
    }

    public class FinancialReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCostOfGoods { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal NetProfit { get; set; }
        public List<ExpenseSummaryByCategory> ExpensesByCategory { get; set; } = new();
        public IEnumerable<MonthlyProfitSummary> MonthlyProfitSummary { get; set; } = new List<MonthlyProfitSummary>();

        public decimal GrossProfitMargin => TotalRevenue > 0 ? Math.Round((GrossProfit / TotalRevenue) * 100, 2) : 0;
        public decimal NetProfitMargin => TotalRevenue > 0 ? Math.Round((NetProfit / TotalRevenue) * 100, 2) : 0;
    }

    public class MonthlyProfitSummary
    {
        public string Month { get; set; } = string.Empty;
        public string MonthName { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal CostOfGoods { get; set; }
        public decimal Expenses { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal NetProfit { get; set; }

        public decimal GrossProfitMargin => Revenue > 0 ? Math.Round((GrossProfit / Revenue) * 100, 2) : 0;
        public decimal NetProfitMargin => Revenue > 0 ? Math.Round((NetProfit / Revenue) * 100, 2) : 0;
    }

    public class CashFlowReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<CashFlowItem> CashInflows { get; set; } = new();
        public List<CashFlowItem> CashOutflows { get; set; } = new();
        public decimal NetCashFlow { get; set; }
    }

    public class CashFlowItem
    {
        public string Category { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = string.Empty;
        public decimal Amount { get; set; }
    }

    public class CustomerReport
    {
        public int TotalCustomers { get; set; }
        public decimal TotalOutstandingBalance { get; set; }
        public List<Customer> CustomersWithOutstandingBalance { get; set; } = new();
        public List<Customer> TopCustomers { get; set; } = new();
    }

    public class SupplierReport
    {
        public int TotalSuppliers { get; set; }
        public decimal TotalOutstandingBalance { get; set; }
        public List<Supplier> SuppliersWithOutstandingBalance { get; set; } = new();
        public List<Supplier> TopSuppliers { get; set; } = new();
    }

    public class EmployeeReport
    {
        public int TotalEmployees { get; set; }
        public int ActiveEmployees { get; set; }
        public decimal TotalSalaries { get; set; }
        public IEnumerable<PositionCount> EmployeesByPosition { get; set; } = new List<PositionCount>();
    }

    public class PositionCount
    {
        public string Position { get; set; } = string.Empty;
        public int Count { get; set; }
    }

    public class DashboardData
    {
        public decimal TodaySales { get; set; }
        public decimal CurrentMonthSales { get; set; }
        public decimal SalesGrowth { get; set; }

        public decimal TodayPurchases { get; set; }
        public decimal CurrentMonthPurchases { get; set; }
        public decimal PurchasesGrowth { get; set; }

        public decimal TodayExpenses { get; set; }
        public decimal CurrentMonthExpenses { get; set; }
        public decimal ExpensesGrowth { get; set; }

        public decimal CurrentMonthProfit { get; set; }
        public decimal ProfitGrowth { get; set; }

        public List<Product> LowStockProducts { get; set; } = new();
        public List<Sale> RecentSales { get; set; } = new();
        public List<SaleSummaryByProduct> TopSellingProducts { get; set; } = new();
        public List<Customer> CustomersWithOutstandingBalance { get; set; } = new();
    }

    #endregion
}