using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class ExpenseService
    {
        private readonly DatabaseService _dbService;

        public ExpenseService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<IEnumerable<Expense>> GetAllExpensesAsync()
        {
            const string sql = @"
                SELECT e.*, ec.Name as CategoryName 
                FROM Expenses e
                LEFT JOIN ExpenseCategories ec ON e.CategoryId = ec.Id
                ORDER BY e.Date DESC";

            return await _dbService.QueryAsync<Expense>(sql);
        }

        public async Task<IEnumerable<Expense>> GetExpensesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT e.*, ec.Name as CategoryName 
                FROM Expenses e
                LEFT JOIN ExpenseCategories ec ON e.CategoryId = ec.Id
                WHERE e.Date BETWEEN @StartDate AND @EndDate
                ORDER BY e.Date DESC";

            return await _dbService.QueryAsync<Expense>(sql, new
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            });
        }

        public async Task<IEnumerable<Expense>> GetExpensesByCategoryAsync(int categoryId)
        {
            const string sql = @"
                SELECT e.*, ec.Name as CategoryName 
                FROM Expenses e
                LEFT JOIN ExpenseCategories ec ON e.CategoryId = ec.Id
                WHERE e.CategoryId = @CategoryId
                ORDER BY e.Date DESC";

            return await _dbService.QueryAsync<Expense>(sql, new { CategoryId = categoryId });
        }

        public async Task<IEnumerable<Expense>> SearchExpensesAsync(string searchTerm)
        {
            const string sql = @"
                SELECT e.*, ec.Name as CategoryName 
                FROM Expenses e
                LEFT JOIN ExpenseCategories ec ON e.CategoryId = ec.Id
                WHERE e.Title LIKE @SearchTerm OR e.Notes LIKE @SearchTerm OR ec.Name LIKE @SearchTerm
                ORDER BY e.Date DESC";

            return await _dbService.QueryAsync<Expense>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<Expense> GetExpenseByIdAsync(int id)
        {
            const string sql = @"
                SELECT e.*, ec.Name as CategoryName 
                FROM Expenses e
                LEFT JOIN ExpenseCategories ec ON e.CategoryId = ec.Id
                WHERE e.Id = @Id";

            return await _dbService.QuerySingleOrDefaultAsync<Expense>(sql, new { Id = id });
        }

        public async Task<Expense> AddExpenseAsync(Expense expense)
        {
            expense.CreatedAt = DateTime.Now;
            return await _dbService.InsertAsync<Expense>("Expenses", expense);
        }

        public async Task<bool> UpdateExpenseAsync(Expense expense)
        {
            expense.UpdatedAt = DateTime.Now;
            return await _dbService.UpdateAsync("Expenses", expense);
        }

        public async Task<bool> DeleteExpenseAsync(int id)
        {
            return await _dbService.DeleteAsync("Expenses", id);
        }

        public async Task<decimal> GetTotalExpensesAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            string dateFilter = "";
            if (startDate.HasValue && endDate.HasValue)
            {
                dateFilter = "WHERE Date BETWEEN @StartDate AND @EndDate";
            }

            string sql = $"SELECT SUM(Amount) FROM Expenses {dateFilter}";

            var result = await _dbService.QuerySingleOrDefaultAsync<decimal?>(sql, new
            {
                StartDate = startDate?.ToString("yyyy-MM-dd"),
                EndDate = endDate?.ToString("yyyy-MM-dd")
            });

            return result ?? 0;
        }

        public async Task<IEnumerable<ExpenseSummaryByCategory>> GetExpenseSummaryByCategoryAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT ec.Id, ec.Name, SUM(e.Amount) as TotalAmount
                FROM Expenses e
                LEFT JOIN ExpenseCategories ec ON e.CategoryId = ec.Id
                WHERE e.Date BETWEEN @StartDate AND @EndDate
                GROUP BY ec.Id, ec.Name
                ORDER BY TotalAmount DESC";

            return await _dbService.QueryAsync<ExpenseSummaryByCategory>(sql, new
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            });
        }

        public async Task<IEnumerable<ExpenseSummaryByMonth>> GetExpenseSummaryByMonthAsync(int year)
        {
            const string sql = @"
                SELECT strftime('%m', Date) as Month, SUM(Amount) as TotalAmount
                FROM Expenses
                WHERE strftime('%Y', Date) = @Year
                GROUP BY strftime('%m', Date)
                ORDER BY Month";

            return await _dbService.QueryAsync<ExpenseSummaryByMonth>(sql, new { Year = year.ToString() });
        }

        public async Task<IEnumerable<ExpenseCategory>> GetAllExpenseCategoriesAsync()
        {
            const string sql = "SELECT * FROM ExpenseCategories ORDER BY Name";
            return await _dbService.QueryAsync<ExpenseCategory>(sql);
        }

        public async Task<ExpenseCategory> GetExpenseCategoryByIdAsync(int id)
        {
            const string sql = "SELECT * FROM ExpenseCategories WHERE Id = @Id";
            return await _dbService.QuerySingleOrDefaultAsync<ExpenseCategory>(sql, new { Id = id });
        }

        public async Task<ExpenseCategory> AddExpenseCategoryAsync(ExpenseCategory category)
        {
            return await _dbService.InsertAsync<ExpenseCategory>("ExpenseCategories", category);
        }

        public async Task<bool> UpdateExpenseCategoryAsync(ExpenseCategory category)
        {
            return await _dbService.UpdateAsync("ExpenseCategories", category);
        }

        public async Task<bool> DeleteExpenseCategoryAsync(int id)
        {
            return await _dbService.DeleteAsync("ExpenseCategories", id);
        }
    }

    public class ExpenseSummaryByCategory
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class ExpenseSummaryByMonth
    {
        public string Month { get; set; }
        public decimal TotalAmount { get; set; }

        public string MonthName => int.TryParse(Month, out int monthNumber) 
            ? new DateTime(2000, monthNumber, 1).ToString("MMMM") 
            : Month;
    }
}