using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class SettingsService
    {
        private readonly DatabaseService _dbService;
        private Dictionary<string, string> _cachedSettings;

        public SettingsService(DatabaseService dbService)
        {
            _dbService = dbService;
            _cachedSettings = new Dictionary<string, string>();
        }

        public async Task InitializeAsync()
        {
            // Load all settings into cache
            await RefreshCacheAsync();

            // Apply language setting
            await ApplyLanguageSettingAsync();
        }

        private async Task RefreshCacheAsync()
        {
            const string sql = "SELECT * FROM Settings";
            var settings = await _dbService.QueryAsync<Setting>(sql);

            _cachedSettings.Clear();
            foreach (var setting in settings)
            {
                _cachedSettings[setting.Key] = setting.Value;
            }
        }

        public async Task<IEnumerable<Setting>> GetAllSettingsAsync()
        {
            const string sql = "SELECT * FROM Settings ORDER BY Key";
            return await _dbService.QueryAsync<Setting>(sql);
        }

        public async Task<string> GetSettingAsync(string key, string defaultValue = null)
        {
            // Try to get from cache first
            if (_cachedSettings.TryGetValue(key, out string value))
            {
                return value;
            }

            // If not in cache, try to get from database
            const string sql = "SELECT Value FROM Settings WHERE Key = @Key";
            value = await _dbService.QuerySingleOrDefaultAsync<string>(sql, new { Key = key });

            // Update cache
            if (value != null)
            {
                _cachedSettings[key] = value;
                return value;
            }

            // Return default value if setting not found
            return defaultValue;
        }

        public async Task<bool> SetSettingAsync(string key, string value)
        {
            // Check if setting exists
            const string checkSql = "SELECT COUNT(*) FROM Settings WHERE Key = @Key";
            int count = await _dbService.QuerySingleOrDefaultAsync<int>(checkSql, new { Key = key });

            bool result;
            if (count > 0)
            {
                // Update existing setting
                const string updateSql = "UPDATE Settings SET Value = @Value, UpdatedAt = @UpdatedAt WHERE Key = @Key";
                result = await _dbService.ExecuteAsync(updateSql, new
                {
                    Key = key,
                    Value = value,
                    UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                }) > 0;
            }
            else
            {
                // Insert new setting
                const string insertSql = "INSERT INTO Settings (Key, Value, CreatedAt) VALUES (@Key, @Value, @CreatedAt)";
                result = await _dbService.ExecuteAsync(insertSql, new
                {
                    Key = key,
                    Value = value,
                    CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                }) > 0;
            }

            // Update cache if successful
            if (result)
            {
                _cachedSettings[key] = value;

                // Apply special settings immediately
                if (key == "Language")
                {
                    await ApplyLanguageSettingAsync();
                }
            }

            return result;
        }

        public async Task<bool> DeleteSettingAsync(string key)
        {
            const string sql = "DELETE FROM Settings WHERE Key = @Key";
            bool result = await _dbService.ExecuteAsync(sql, new { Key = key }) > 0;

            // Remove from cache if successful
            if (result && _cachedSettings.ContainsKey(key))
            {
                _cachedSettings.Remove(key);
            }

            return result;
        }

        #region Application Settings

        public async Task<string> GetCompanyNameAsync()
        {
            return await GetSettingAsync("CompanyName", "Sales Management System");
        }

        public async Task<string> GetCompanyAddressAsync()
        {
            return await GetSettingAsync("CompanyAddress", "");
        }

        public async Task<string> GetCompanyPhoneAsync()
        {
            return await GetSettingAsync("CompanyPhone", "");
        }

        public async Task<string> GetCompanyEmailAsync()
        {
            return await GetSettingAsync("CompanyEmail", "");
        }

        public async Task<string> GetCurrencySymbolAsync()
        {
            return await GetSettingAsync("CurrencySymbol", "$");
        }

        public async Task<string> GetLanguageAsync()
        {
            return await GetSettingAsync("Language", "en-US");
        }

        public async Task<decimal> GetTaxRateAsync()
        {
            string taxRateStr = await GetSettingAsync("TaxRate", "0");
            if (decimal.TryParse(taxRateStr, out decimal taxRate))
            {
                return taxRate;
            }
            return 0;
        }

        public async Task<int> GetLowStockThresholdAsync()
        {
            string thresholdStr = await GetSettingAsync("LowStockThreshold", "10");
            if (int.TryParse(thresholdStr, out int threshold))
            {
                return threshold;
            }
            return 10;
        }

        public async Task<bool> GetShowLowStockNotificationsAsync()
        {
            string showNotificationsStr = await GetSettingAsync("ShowLowStockNotifications", "true");
            if (bool.TryParse(showNotificationsStr, out bool showNotifications))
            {
                return showNotifications;
            }
            return true;
        }

        public async Task<string> GetDateFormatAsync()
        {
            return await GetSettingAsync("DateFormat", "yyyy-MM-dd");
        }

        public async Task<string> GetTimeFormatAsync()
        {
            return await GetSettingAsync("TimeFormat", "HH:mm:ss");
        }

        public async Task<string> GetThemeAsync()
        {
            return await GetSettingAsync("Theme", "Light");
        }

        #endregion

        #region Settings Application

        private async Task ApplyLanguageSettingAsync()
        {
            string language = await GetLanguageAsync();
            try
            {
                // Set current culture
                Thread.CurrentThread.CurrentCulture = new CultureInfo(language);
                Thread.CurrentThread.CurrentUICulture = new CultureInfo(language);

                // Set application resources
                ResourceDictionary dict = new ResourceDictionary();
                dict.Source = new Uri($"/Resources/Localization/{language}.xaml", UriKind.Relative);

                // Clear existing dictionaries with the same source
                for (int i = Application.Current.Resources.MergedDictionaries.Count - 1; i >= 0; i--)
                {
                    var resourceDict = Application.Current.Resources.MergedDictionaries[i];
                    if (resourceDict.Source != null && resourceDict.Source.OriginalString.StartsWith("/Resources/Localization/"))
                    {
                        Application.Current.Resources.MergedDictionaries.RemoveAt(i);
                    }
                }

                // Add the new dictionary
                Application.Current.Resources.MergedDictionaries.Add(dict);
            }
            catch (Exception ex)
            {
                // Log error or handle exception
                Console.WriteLine($"Error applying language setting: {ex.Message}");
            }
        }

        public async Task ApplyThemeAsync()
        {
            string theme = await GetThemeAsync();
            try
            {
                // Set application theme
                ResourceDictionary dict = new ResourceDictionary();
                dict.Source = new Uri($"/Resources/Themes/{theme}Theme.xaml", UriKind.Relative);

                // Clear existing theme dictionaries
                for (int i = Application.Current.Resources.MergedDictionaries.Count - 1; i >= 0; i--)
                {
                    var resourceDict = Application.Current.Resources.MergedDictionaries[i];
                    if (resourceDict.Source != null && resourceDict.Source.OriginalString.StartsWith("/Resources/Themes/"))
                    {
                        Application.Current.Resources.MergedDictionaries.RemoveAt(i);
                    }
                }

                // Add the new dictionary
                Application.Current.Resources.MergedDictionaries.Add(dict);
            }
            catch (Exception ex)
            {
                // Log error or handle exception
                Console.WriteLine($"Error applying theme setting: {ex.Message}");
            }
        }

        #endregion
    }
}