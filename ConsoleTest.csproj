<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.0.123" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.116" />
    <PackageReference Include="FluentValidation" Version="11.2.2" />
  </ItemGroup>

  <PropertyGroup>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="Services\DatabaseService.cs" />
    <Compile Include="Services\ProductService.cs" />
    <Compile Include="Services\CustomerService.cs" />
    <Compile Include="Services\EmployeeService.cs" />
    <Compile Include="Services\SupplierService.cs" />
    <Compile Include="Services\SaleService.cs" />
    <Compile Include="Services\PurchaseService.cs" />
    <Compile Include="Services\ExpenseService.cs" />
    <Compile Include="Services\UserService.cs" />
    <Compile Include="Models\*.cs" />
    <Compile Include="ConsoleTest.cs" />
  </ItemGroup>

</Project>
