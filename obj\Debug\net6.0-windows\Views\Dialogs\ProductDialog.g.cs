﻿#pragma checksum "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DE7F24124168F574B711AF61B597D2CA340D12E1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SalesManagementSystem.Views.Dialogs {
    
    
    /// <summary>
    /// ProductDialog
    /// </summary>
    public partial class ProductDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 22 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderText;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PurchasePriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SellingPriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinQuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProfitMarginText;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProfitPercentageText;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalValueText;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SalesManagementSystem;component/views/dialogs/productdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.CodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.CategoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.PurchasePriceTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 74 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
            this.PurchasePriceTextBox.PreviewTextInput += new System.Windows.Input.TextCompositionEventHandler(this.NumericTextBox_PreviewTextInput);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SellingPriceTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 80 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
            this.SellingPriceTextBox.PreviewTextInput += new System.Windows.Input.TextCompositionEventHandler(this.NumericTextBox_PreviewTextInput);
            
            #line default
            #line hidden
            return;
            case 8:
            this.QuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 95 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
            this.QuantityTextBox.PreviewTextInput += new System.Windows.Input.TextCompositionEventHandler(this.IntegerTextBox_PreviewTextInput);
            
            #line default
            #line hidden
            return;
            case 9:
            this.MinQuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 101 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
            this.MinQuantityTextBox.PreviewTextInput += new System.Windows.Input.TextCompositionEventHandler(this.IntegerTextBox_PreviewTextInput);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ProfitMarginText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ProfitPercentageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TotalValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 151 "..\..\..\..\..\Views\Dialogs\ProductDialog.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

