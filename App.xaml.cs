using System;
using System.Globalization;
using System.Threading;
using System.Windows;
using System.Windows.Markup;

namespace SalesManagementSystem
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        public static bool IsArabic { get; private set; } = true;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Set application culture and UI direction
            SetApplicationCulture(IsArabic ? "ar-SA" : "en-US");

            // Initialize services
            InitializeServices();
        }

        public static void SetApplicationCulture(string cultureName)
        {
            IsArabic = cultureName.StartsWith("ar");

            // Set the culture for the current thread
            Thread.CurrentThread.CurrentCulture = new CultureInfo(cultureName);
            Thread.CurrentThread.CurrentUICulture = new CultureInfo(cultureName);

            // Set FlowDirection for XAML
            FrameworkElement.FlowDirectionProperty.OverrideMetadata(
                typeof(FrameworkElement),
                new FrameworkPropertyMetadata(IsArabic ? FlowDirection.RightToLeft : FlowDirection.LeftToRight));

            // Set language for XAML
            XmlLanguage language = XmlLanguage.GetLanguage(cultureName);
            FrameworkElement.LanguageProperty.OverrideMetadata(
                typeof(FrameworkElement),
                new FrameworkPropertyMetadata(language));

            // Reload resources
            ResourceDictionary resourceDictionary = new ResourceDictionary
            {
                Source = new Uri($"/Resources/Localization/{cultureName}.xaml", UriKind.Relative)
            };

            // Replace the localization dictionary
            ResourceDictionary? oldDictionary = null;
            foreach (ResourceDictionary dict in Current.Resources.MergedDictionaries)
            {
                if (dict.Source != null && dict.Source.OriginalString.Contains("/Resources/Localization/"))
                {
                    oldDictionary = dict;
                    break;
                }
            }

            if (oldDictionary != null)
            {
                int index = Current.Resources.MergedDictionaries.IndexOf(oldDictionary);
                Current.Resources.MergedDictionaries.Remove(oldDictionary);
                Current.Resources.MergedDictionaries.Insert(index, resourceDictionary);
            }
            else
            {
                Current.Resources.MergedDictionaries.Add(resourceDictionary);
            }
        }

        private void InitializeServices()
        {
            // Initialize database service
            // TODO: Implement database initialization

            // Initialize other services
            // TODO: Implement other services initialization
        }
    }
}