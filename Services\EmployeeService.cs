using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class EmployeeService
    {
        private readonly DatabaseService _dbService;

        public EmployeeService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<IEnumerable<Employee>> GetAllEmployeesAsync()
        {
            const string sql = "SELECT * FROM Employees ORDER BY Name";
            return await _dbService.QueryAsync<Employee>(sql);
        }

        public async Task<IEnumerable<Employee>> GetActiveEmployeesAsync()
        {
            const string sql = "SELECT * FROM Employees WHERE Status = 'Active' ORDER BY Name";
            return await _dbService.QueryAsync<Employee>(sql);
        }

        public async Task<IEnumerable<Employee>> SearchEmployeesAsync(string searchTerm)
        {
            const string sql = @"
                SELECT * FROM Employees
                WHERE Name LIKE @SearchTerm OR Position LIKE @SearchTerm OR Phone LIKE @SearchTerm OR Email LIKE @SearchTerm
                ORDER BY Name";

            return await _dbService.QueryAsync<Employee>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<Employee> GetEmployeeByIdAsync(int id)
        {
            const string sql = "SELECT * FROM Employees WHERE Id = @Id";
            return await _dbService.QuerySingleOrDefaultAsync<Employee>(sql, new { Id = id });
        }

        public async Task<Employee> AddEmployeeAsync(Employee employee)
        {
            employee.CreatedAt = DateTime.Now;
            return await _dbService.InsertAsync<Employee>("Employees", employee);
        }

        public async Task<bool> UpdateEmployeeAsync(Employee employee)
        {
            employee.UpdatedAt = DateTime.Now;
            return await _dbService.UpdateAsync("Employees", employee);
        }

        public async Task<bool> DeleteEmployeeAsync(int id)
        {
            return await _dbService.DeleteAsync("Employees", id);
        }

        public async Task<bool> UpdateEmployeeStatusAsync(int employeeId, string status)
        {
            const string sql = @"
                UPDATE Employees
                SET Status = @Status, UpdatedAt = @UpdatedAt
                WHERE Id = @EmployeeId";

            var result = await _dbService.ExecuteAsync(sql, new
            {
                EmployeeId = employeeId,
                Status = status,
                UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });

            return result > 0;
        }

        public async Task<decimal> GetTotalSalariesAsync()
        {
            const string sql = "SELECT SUM(Salary) FROM Employees WHERE Status = 'Active'";
            var result = await _dbService.QuerySingleOrDefaultAsync<decimal?>(sql);
            return result ?? 0;
        }

        public async Task<IEnumerable<EmployeeAttendance>> GetEmployeeAttendanceAsync(int employeeId, DateTime startDate, DateTime endDate)
        {
            // This is a placeholder for attendance functionality
            // In a real implementation, you would have an Attendance table in the database
            // For now, we'll return an empty list
            return new List<EmployeeAttendance>();
        }

        public async Task<bool> RecordAttendanceAsync(EmployeeAttendance attendance)
        {
            // This is a placeholder for attendance functionality
            // In a real implementation, you would insert into an Attendance table
            return true;
        }

        public async Task<IEnumerable<EmployeePerformance>> GetEmployeePerformanceAsync(int employeeId, DateTime startDate, DateTime endDate)
        {
            // This is a placeholder for performance tracking functionality
            // In a real implementation, you would have a Performance table in the database
            // For now, we'll return an empty list
            return new List<EmployeePerformance>();
        }

        public async Task<bool> RecordPerformanceAsync(EmployeePerformance performance)
        {
            // This is a placeholder for performance tracking functionality
            // In a real implementation, you would insert into a Performance table
            return true;
        }

        // Additional methods for ReportService
        public async Task<int> GetEmployeeCountAsync()
        {
            const string sql = "SELECT COUNT(*) FROM Employees";
            return await _dbService.QuerySingleAsync<int>(sql);
        }

        public async Task<int> GetActiveEmployeeCountAsync()
        {
            const string sql = "SELECT COUNT(*) FROM Employees WHERE Status = 'Active'";
            return await _dbService.QuerySingleAsync<int>(sql);
        }

        public async Task<IEnumerable<EmployeePositionCount>> GetEmployeeCountByPositionAsync()
        {
            const string sql = @"
                SELECT Position, COUNT(*) as EmployeeCount
                FROM Employees
                WHERE Status = 'Active'
                GROUP BY Position
                ORDER BY EmployeeCount DESC";

            return await _dbService.QueryAsync<EmployeePositionCount>(sql);
        }
    }

    public class EmployeeAttendance
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public DateTime Date { get; set; }
        public TimeSpan CheckIn { get; set; }
        public TimeSpan? CheckOut { get; set; }
        public string Status { get; set; } // Present, Absent, Late, etc.
        public string Notes { get; set; }
    }

    public class EmployeePerformance
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public DateTime Date { get; set; }
        public int Rating { get; set; } // 1-5 scale
        public string Feedback { get; set; }
        public string EvaluatedBy { get; set; }
    }

    public class EmployeePositionCount
    {
        public string Position { get; set; }
        public int EmployeeCount { get; set; }
    }
}