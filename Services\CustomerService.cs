using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class CustomerService
    {
        private readonly DatabaseService _dbService;

        public CustomerService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<IEnumerable<Customer>> GetAllCustomersAsync()
        {
            const string sql = "SELECT * FROM Customers ORDER BY Name";
            return await _dbService.QueryAsync<Customer>(sql);
        }

        public async Task<IEnumerable<Customer>> GetCustomersWithOutstandingBalanceAsync()
        {
            const string sql = "SELECT * FROM Customers WHERE Balance > 0 ORDER BY Balance DESC";
            return await _dbService.QueryAsync<Customer>(sql);
        }

        public async Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm)
        {
            const string sql = @"
                SELECT * FROM Customers
                WHERE Name LIKE @SearchTerm OR Phone LIKE @SearchTerm OR Email LIKE @SearchTerm
                ORDER BY Name";

            return await _dbService.QueryAsync<Customer>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<Customer> GetCustomerByIdAsync(int id)
        {
            const string sql = "SELECT * FROM Customers WHERE Id = @Id";
            return await _dbService.QuerySingleOrDefaultAsync<Customer>(sql, new { Id = id });
        }

        public async Task<Customer> AddCustomerAsync(Customer customer)
        {
            customer.CreatedAt = DateTime.Now;
            return await _dbService.InsertAsync<Customer>("Customers", customer);
        }

        public async Task<bool> UpdateCustomerAsync(Customer customer)
        {
            customer.UpdatedAt = DateTime.Now;
            return await _dbService.UpdateAsync("Customers", customer);
        }

        public async Task<bool> DeleteCustomerAsync(int id)
        {
            return await _dbService.DeleteAsync("Customers", id);
        }

        public async Task<bool> UpdateCustomerBalanceAsync(int customerId, decimal amount)
        {
            const string sql = @"
                UPDATE Customers
                SET Balance = Balance + @Amount, UpdatedAt = @UpdatedAt
                WHERE Id = @CustomerId";

            var result = await _dbService.ExecuteAsync(sql, new
            {
                CustomerId = customerId,
                Amount = amount,
                UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });

            return result > 0;
        }

        public async Task<IEnumerable<CustomerSalesHistory>> GetCustomerSalesHistoryAsync(int customerId)
        {
            const string sql = @"
                SELECT s.Id, s.InvoiceNumber, s.Date, s.Total, s.PaymentStatus,
                       COUNT(si.Id) as ItemCount
                FROM Sales s
                LEFT JOIN SaleItems si ON s.Id = si.SaleId
                WHERE s.CustomerId = @CustomerId
                GROUP BY s.Id
                ORDER BY s.Date DESC";

            return await _dbService.QueryAsync<CustomerSalesHistory>(sql, new { CustomerId = customerId });
        }

        public async Task<IEnumerable<Customer>> GetTopCustomersAsync(int count = 10, DateTime? startDate = null, DateTime? endDate = null)
        {
            string dateFilter = "";
            if (startDate.HasValue && endDate.HasValue)
            {
                dateFilter = "AND s.Date BETWEEN @StartDate AND @EndDate";
            }

            string sql = $@"
                SELECT c.*, SUM(s.Total) as TotalPurchases
                FROM Customers c
                INNER JOIN Sales s ON c.Id = s.CustomerId
                WHERE 1=1 {dateFilter}
                GROUP BY c.Id
                ORDER BY TotalPurchases DESC
                LIMIT @Count";

            return await _dbService.QueryAsync<Customer>(sql, new
            {
                Count = count,
                StartDate = startDate?.ToString("yyyy-MM-dd"),
                EndDate = endDate?.ToString("yyyy-MM-dd")
            });
        }

        // Additional methods for ReportService
        public async Task<int> GetCustomerCountAsync()
        {
            const string sql = "SELECT COUNT(*) FROM Customers";
            return await _dbService.QuerySingleAsync<int>(sql);
        }

        public async Task<decimal> GetTotalCustomerBalanceAsync()
        {
            const string sql = "SELECT SUM(Balance) FROM Customers WHERE Balance > 0";
            var result = await _dbService.QuerySingleOrDefaultAsync<decimal?>(sql);
            return result ?? 0;
        }
    }

    public class CustomerSalesHistory
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime Date { get; set; }
        public decimal Total { get; set; }
        public string PaymentStatus { get; set; }
        public int ItemCount { get; set; }
    }
}