using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Xml.Linq;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class TranslationService
    {
        private readonly DatabaseService _dbService;
        private readonly SettingsService _settingsService;
        private Dictionary<string, Dictionary<string, string>> _translations;

        public TranslationService(DatabaseService dbService, SettingsService settingsService)
        {
            _dbService = dbService;
            _settingsService = settingsService;
            _translations = new Dictionary<string, Dictionary<string, string>>();
        }

        public async Task InitializeAsync()
        {
            // Load translations from database
            await LoadTranslationsAsync();

            // Apply current language
            await ApplyCurrentLanguageAsync();
        }

        private async Task LoadTranslationsAsync()
        {
            const string sql = "SELECT * FROM Translations";
            var translations = await _dbService.QueryAsync<Translation>(sql);

            _translations.Clear();

            foreach (var translation in translations)
            {
                if (!_translations.ContainsKey(translation.LanguageCode))
                {
                    _translations[translation.LanguageCode] = new Dictionary<string, string>();
                }

                _translations[translation.LanguageCode][translation.Key] = translation.Value;
            }
        }

        public async Task<IEnumerable<Translation>> GetAllTranslationsAsync()
        {
            const string sql = "SELECT * FROM Translations ORDER BY LanguageCode, Key";
            return await _dbService.QueryAsync<Translation>(sql);
        }

        public async Task<IEnumerable<Translation>> GetTranslationsByLanguageAsync(string languageCode)
        {
            const string sql = "SELECT * FROM Translations WHERE LanguageCode = @LanguageCode ORDER BY Key";
            return await _dbService.QueryAsync<Translation>(sql, new { LanguageCode = languageCode });
        }

        public async Task<Translation> GetTranslationAsync(string languageCode, string key)
        {
            const string sql = "SELECT * FROM Translations WHERE LanguageCode = @LanguageCode AND Key = @Key";
            return await _dbService.QuerySingleOrDefaultAsync<Translation>(sql, new { LanguageCode = languageCode, Key = key });
        }

        public async Task<bool> AddTranslationAsync(Translation translation)
        {
            // Validate translation
            var validator = new TranslationValidator();
            var validationResult = validator.Validate(translation);
            if (!validationResult.IsValid)
            {
                throw new ValidationException(validationResult.Errors);
            }

            // Check if translation already exists
            var existingTranslation = await GetTranslationAsync(translation.LanguageCode, translation.Key);
            if (existingTranslation != null)
            {
                return false; // Translation already exists
            }

            // Add new translation
            const string sql = @"INSERT INTO Translations (LanguageCode, Key, Value, CreatedAt) 
                               VALUES (@LanguageCode, @Key, @Value, @CreatedAt)";

            translation.CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            int result = await _dbService.ExecuteAsync(sql, translation);

            if (result > 0)
            {
                // Update in-memory cache
                if (!_translations.ContainsKey(translation.LanguageCode))
                {
                    _translations[translation.LanguageCode] = new Dictionary<string, string>();
                }

                _translations[translation.LanguageCode][translation.Key] = translation.Value;

                // Update resource dictionary if it's the current language
                string currentLanguage = await _settingsService.GetLanguageAsync();
                if (translation.LanguageCode == currentLanguage)
                {
                    UpdateResourceDictionary(translation.Key, translation.Value);
                }
            }

            return result > 0;
        }

        public async Task<bool> UpdateTranslationAsync(Translation translation)
        {
            // Validate translation
            var validator = new TranslationValidator();
            var validationResult = validator.Validate(translation);
            if (!validationResult.IsValid)
            {
                throw new ValidationException(validationResult.Errors);
            }

            // Update translation
            const string sql = @"UPDATE Translations 
                               SET Value = @Value, UpdatedAt = @UpdatedAt 
                               WHERE LanguageCode = @LanguageCode AND Key = @Key";

            translation.UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            int result = await _dbService.ExecuteAsync(sql, translation);

            if (result > 0)
            {
                // Update in-memory cache
                if (!_translations.ContainsKey(translation.LanguageCode))
                {
                    _translations[translation.LanguageCode] = new Dictionary<string, string>();
                }

                _translations[translation.LanguageCode][translation.Key] = translation.Value;

                // Update resource dictionary if it's the current language
                string currentLanguage = await _settingsService.GetLanguageAsync();
                if (translation.LanguageCode == currentLanguage)
                {
                    UpdateResourceDictionary(translation.Key, translation.Value);
                }
            }

            return result > 0;
        }

        public async Task<bool> DeleteTranslationAsync(string languageCode, string key)
        {
            const string sql = "DELETE FROM Translations WHERE LanguageCode = @LanguageCode AND Key = @Key";
            int result = await _dbService.ExecuteAsync(sql, new { LanguageCode = languageCode, Key = key });

            if (result > 0 && _translations.ContainsKey(languageCode))
            {
                // Remove from in-memory cache
                _translations[languageCode].Remove(key);
            }

            return result > 0;
        }

        public async Task<bool> ChangeLanguageAsync(string languageCode)
        {
            // Save language setting
            bool result = await _settingsService.SetSettingAsync("Language", languageCode);

            if (result)
            {
                // Apply language change
                await ApplyCurrentLanguageAsync();
            }

            return result;
        }

        private async Task ApplyCurrentLanguageAsync()
        {
            string languageCode = await _settingsService.GetLanguageAsync();

            try
            {
                // Set current culture
                Thread.CurrentThread.CurrentCulture = new CultureInfo(languageCode);
                Thread.CurrentThread.CurrentUICulture = new CultureInfo(languageCode);

                // Apply translations to resource dictionary
                ApplyTranslationsToResourceDictionary(languageCode);
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error applying language: {ex.Message}");
            }
        }

        private void ApplyTranslationsToResourceDictionary(string languageCode)
        {
            if (!_translations.ContainsKey(languageCode))
            {
                return;
            }

            // Create or get resource dictionary for language
            ResourceDictionary resourceDict = null;

            // Look for existing dictionary
            foreach (ResourceDictionary dict in Application.Current.Resources.MergedDictionaries)
            {
                if (dict.Source != null && dict.Source.OriginalString.Contains($"{languageCode}.xaml"))
                {
                    resourceDict = dict;
                    break;
                }
            }

            // If not found, try to load from file
            if (resourceDict == null)
            {
                try
                {
                    resourceDict = new ResourceDictionary();
                    resourceDict.Source = new Uri($"/Resources/Localization/{languageCode}.xaml", UriKind.Relative);
                }
                catch
                {
                    // If file doesn't exist, create new dictionary
                    resourceDict = new ResourceDictionary();
                }
            }

            // Apply translations
            foreach (var entry in _translations[languageCode])
            {
                resourceDict[entry.Key] = entry.Value;
            }

            // Add or replace in application resources
            bool found = false;
            for (int i = 0; i < Application.Current.Resources.MergedDictionaries.Count; i++)
            {
                var dict = Application.Current.Resources.MergedDictionaries[i];
                if (dict.Source != null && dict.Source.OriginalString.Contains("/Resources/Localization/"))
                {
                    Application.Current.Resources.MergedDictionaries[i] = resourceDict;
                    found = true;
                    break;
                }
            }

            if (!found)
            {
                Application.Current.Resources.MergedDictionaries.Add(resourceDict);
            }
        }

        private void UpdateResourceDictionary(string key, string value)
        {
            // Find language resource dictionary
            foreach (ResourceDictionary dict in Application.Current.Resources.MergedDictionaries)
            {
                if (dict.Source != null && dict.Source.OriginalString.Contains("/Resources/Localization/"))
                {
                    // Update or add key
                    dict[key] = value;
                    break;
                }
            }
        }

        public async Task<bool> ImportTranslationsAsync(string filePath, string languageCode)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return false;
                }

                // Load XML file
                XDocument doc = XDocument.Load(filePath);
                var entries = doc.Root.Elements("data");

                int importedCount = 0;
                foreach (var entry in entries)
                {
                    string key = entry.Attribute("name")?.Value;
                    string value = entry.Element("value")?.Value;

                    if (!string.IsNullOrEmpty(key) && !string.IsNullOrEmpty(value))
                    {
                        // Check if translation exists
                        var existingTranslation = await GetTranslationAsync(languageCode, key);

                        if (existingTranslation != null)
                        {
                            // Update existing translation
                            existingTranslation.Value = value;
                            await UpdateTranslationAsync(existingTranslation);
                        }
                        else
                        {
                            // Add new translation
                            var translation = new Translation
                            {
                                LanguageCode = languageCode,
                                Key = key,
                                Value = value
                            };

                            await AddTranslationAsync(translation);
                        }

                        importedCount++;
                    }
                }

                return importedCount > 0;
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error importing translations: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> ExportTranslationsAsync(string filePath, string languageCode)
        {
            try
            {
                // Get translations for language
                var translations = await GetTranslationsByLanguageAsync(languageCode);

                // Create XML document
                XDocument doc = new XDocument(
                    new XElement("root",
                        new XElement("resheader",
                            new XAttribute("name", "resmimetype"),
                            new XElement("value", "text/microsoft-resx")),
                        new XElement("resheader",
                            new XAttribute("name", "version"),
                            new XElement("value", "2.0")),
                        new XElement("resheader",
                            new XAttribute("name", "reader"),
                            new XElement("value", "System.Resources.ResXResourceReader")),
                        new XElement("resheader",
                            new XAttribute("name", "writer"),
                            new XElement("value", "System.Resources.ResXResourceWriter"))
                    )
                );

                // Add translations
                foreach (var translation in translations)
                {
                    doc.Root.Add(
                        new XElement("data",
                            new XAttribute("name", translation.Key),
                            new XElement("value", translation.Value)
                        )
                    );
                }

                // Save to file
                doc.Save(filePath);

                return true;
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error exporting translations: {ex.Message}");
                return false;
            }
        }

        public async Task<IEnumerable<string>> GetAvailableLanguagesAsync()
        {
            const string sql = "SELECT DISTINCT LanguageCode FROM Translations ORDER BY LanguageCode";
            return await _dbService.QueryAsync<string>(sql);
        }

        public string Translate(string key, string defaultValue = "")
        {
            string languageCode = Thread.CurrentThread.CurrentUICulture.Name;

            if (_translations.ContainsKey(languageCode) && _translations[languageCode].ContainsKey(key))
            {
                return _translations[languageCode][key];
            }

            // Fallback to default language (en-US)
            if (languageCode != "en-US" && _translations.ContainsKey("en-US") && _translations["en-US"].ContainsKey(key))
            {
                return _translations["en-US"][key];
            }

            // Return default value or key if translation not found
            return defaultValue ?? key;
        }
    }

    // Custom exception for validation errors
    public class ValidationException : Exception
    {
        public IEnumerable<FluentValidation.Results.ValidationFailure> Errors { get; }

        // Removed duplicate constructor
        public ValidationException(IEnumerable<FluentValidation.Results.ValidationFailure> errors)
            : base("Validation failed")
        {
            Errors = errors;
        }
    }
}