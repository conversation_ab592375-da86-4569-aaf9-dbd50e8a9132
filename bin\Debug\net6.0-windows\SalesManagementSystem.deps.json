{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"SalesManagementSystem/1.0.0": {"dependencies": {"Dapper": "2.0.123", "FluentValidation": "11.2.2", "LiveChartsCore.SkiaSharpView.WPF": "2.0.0-rc2", "MahApps.Metro": "2.4.9", "MaterialDesignThemes": "4.6.1", "NLog": "5.0.4", "Prism.Core": "8.1.97", "System.Data.SQLite": "1.0.116"}, "runtime": {"SalesManagementSystem.dll": {}}}, "ControlzEx/4.4.0": {"dependencies": {"Microsoft.Xaml.Behaviors.Wpf": "1.1.19"}, "runtime": {"lib/netcoreapp3.1/ControlzEx.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.4.0.50"}}}, "Dapper/2.0.123": {"runtime": {"lib/net5.0/Dapper.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.123.33578"}}}, "EntityFramework/6.4.4": {"dependencies": {"System.Data.SqlClient": "4.8.1"}, "runtime": {"lib/netstandard2.1/EntityFramework.SqlServer.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.400.420.21404"}, "lib/netstandard2.1/EntityFramework.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.400.420.21404"}}}, "FluentValidation/11.2.2": {"runtime": {"lib/net6.0/FluentValidation.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.2.2.0"}}}, "HarfBuzzSharp/7.3.0": {"dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0"}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "7.3.0.0"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0": {"runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0": {"runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "LiveChartsCore/2.0.0-rc2": {"runtime": {"lib/net6.0/LiveChartsCore.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "LiveChartsCore.SkiaSharpView/2.0.0-rc2": {"dependencies": {"LiveChartsCore": "2.0.0-rc2", "SkiaSharp": "2.88.6", "SkiaSharp.HarfBuzz": "2.88.6"}, "runtime": {"lib/net6.0/LiveChartsCore.SkiaSharpView.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "LiveChartsCore.SkiaSharpView.WPF/2.0.0-rc2": {"dependencies": {"LiveChartsCore.SkiaSharpView": "2.0.0-rc2", "SkiaSharp.Views.WPF": "2.88.6"}, "runtime": {"lib/netcoreapp3.1/LiveChartsCore.SkiaSharpView.WPF.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "MahApps.Metro/2.4.9": {"dependencies": {"ControlzEx": "4.4.0"}, "runtime": {"lib/netcoreapp3.1/MahApps.Metro.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.4.9.2"}}, "resources": {"lib/netcoreapp3.1/de/MahApps.Metro.resources.dll": {"locale": "de"}}}, "MaterialDesignColors/2.0.9": {"runtime": {"lib/net6.0/MaterialDesignColors.dll": {"assemblyVersion": "2.0.9.0", "fileVersion": "2.0.9.0"}}}, "MaterialDesignThemes/4.6.1": {"dependencies": {"MaterialDesignColors": "2.0.9"}, "runtime": {"lib/net6.0/MaterialDesignThemes.Wpf.dll": {"assemblyVersion": "4.6.1.0", "fileVersion": "4.6.1.0"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.19": {"runtime": {"lib/netcoreapp3.0/Microsoft.Xaml.Behaviors.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.19.35512"}}}, "NLog/5.0.4": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.4.1331"}}}, "Prism.Core/8.1.97": {"runtime": {"lib/net5.0/Prism.dll": {"assemblyVersion": "8.1.97.5141", "fileVersion": "8.1.97.5141"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "SkiaSharp/2.88.6": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.6", "SkiaSharp.NativeAssets.macOS": "2.88.6"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.6.0"}}}, "SkiaSharp.HarfBuzz/2.88.6": {"dependencies": {"HarfBuzzSharp": "7.3.0", "SkiaSharp": "2.88.6"}, "runtime": {"lib/net6.0/SkiaSharp.HarfBuzz.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.6.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.Views.Desktop.Common/2.88.6": {"dependencies": {"SkiaSharp": "2.88.6"}, "runtime": {"lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.6.0"}}}, "SkiaSharp.Views.WPF/2.88.6": {"dependencies": {"SkiaSharp": "2.88.6", "SkiaSharp.Views.Desktop.Common": "2.88.6"}, "runtime": {"lib/netcoreapp3.1/SkiaSharp.Views.WPF.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.6.0"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.116": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "1.0.116.0", "fileVersion": "1.0.116.0"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.116.0"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.116.0"}}}, "System.Data.SqlClient/4.8.1": {"dependencies": {"runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "4.6.1.1", "fileVersion": "4.700.20.6702"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "4.6.1.1", "fileVersion": "4.700.20.6702"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.6.1.1", "fileVersion": "4.700.20.6702"}}}, "System.Data.SQLite/1.0.116": {"dependencies": {"System.Data.SQLite.Core": "1.0.116", "System.Data.SQLite.EF6": "1.0.116"}}, "System.Data.SQLite.Core/1.0.116": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "1.0.116"}}, "System.Data.SQLite.EF6/1.0.116": {"dependencies": {"EntityFramework": "6.4.4"}, "runtime": {"lib/netstandard2.1/System.Data.SQLite.EF6.dll": {"assemblyVersion": "1.0.116.0", "fileVersion": "1.0.116.0"}}}}}, "libraries": {"SalesManagementSystem/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ControlzEx/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-pZ5z4hYWwE4R13UMCVs6vII//nL7hz+Nwn4oJlnsZJRGqJNy6Z9KnJiTZfly6lKFu0pMc1aWBZpx+VqFTQKP1Q==", "path": "controlzex/4.4.0", "hashPath": "controlzex.4.4.0.nupkg.sha512"}, "Dapper/2.0.123": {"type": "package", "serviceable": true, "sha512": "sha512-RDFF4rBLLmbpi6pwkY7q/M6UXHRJEOerplDGE5jwEkP/JGJnBauAClYavNKJPW1yOTWRPIyfj4is3EaJxQXILQ==", "path": "dapper/2.0.123", "hashPath": "dapper.2.0.123.nupkg.sha512"}, "EntityFramework/6.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-yj1+/4tci7Panu3jKDHYizxwVm0Jvm7b7m057b5h4u8NUHGCR8WIWirBTw+8EptRffwftIWPBeIRGNKD1ewEMQ==", "path": "entityframework/6.4.4", "hashPath": "entityframework.6.4.4.nupkg.sha512"}, "FluentValidation/11.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-5QEte2BUZZF6Sezeekr7Zu1THfgpxU7pmuL2SjIrajuGMah+pxPGiRiLtw+x/tSUSSVmcv9LrUe41lKu4l0n4A==", "path": "fluentvalidation/11.2.2", "hashPath": "fluentvalidation.11.2.2.nupkg.sha512"}, "HarfBuzzSharp/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OrQLaxtZMIeS2yHSUtsKzeSdk9CPaCpyJ/JCs+wLfRGatjE8MLUS6LGj6vdbGRxqRavcXs79C9O3oWe6FJR0JQ==", "path": "harfbuzzsharp/7.3.0", "hashPath": "harfbuzzsharp.7.3.0.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LWcFJ39j+dN0KK8c/GJJZPPZPL9TqT2FA42/LRGqzUMmSm5LYbINOMnPvUr7RuLR6RFSmKIrgrlgObR8G5ho2A==", "path": "harfbuzzsharp.nativeassets.macos/7.3.0", "hashPath": "harfbuzzsharp.nativeassets.macos.7.3.0.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ulEewLMk+dNmbmpy15ny/YusI6JNUWqchF080TV2jgfFBXPXjWm767JleDi/S7hp8eDeEN6GYIIxpvNr5fLvIw==", "path": "harfbuzzsharp.nativeassets.win32/7.3.0", "hashPath": "harfbuzzsharp.nativeassets.win32.7.3.0.nupkg.sha512"}, "LiveChartsCore/2.0.0-rc2": {"type": "package", "serviceable": true, "sha512": "sha512-X55dG3oI3AVOGWUrly+J/bUJGmb/pmB2aTkaWJPyIXI2cr5cqPJG/6GLL17IJ8e3GOofFWgQCbg0Az0+myUQLg==", "path": "livechartscore/2.0.0-rc2", "hashPath": "livechartscore.2.0.0-rc2.nupkg.sha512"}, "LiveChartsCore.SkiaSharpView/2.0.0-rc2": {"type": "package", "serviceable": true, "sha512": "sha512-UVvUa7QAlyiPLuCjcoMKr4dI1FIUE5xW/XjcioSXgsOoqZg0E6Fq3B9k5t6MjIVE6dUU2F3tZwdm5KDiNftQ2A==", "path": "livechartscore.skiasharpview/2.0.0-rc2", "hashPath": "livechartscore.skiasharpview.2.0.0-rc2.nupkg.sha512"}, "LiveChartsCore.SkiaSharpView.WPF/2.0.0-rc2": {"type": "package", "serviceable": true, "sha512": "sha512-FUpPKrCySv3oP/eilTN7ncHKGgacRwh2MK0CHzMEEAZ+77qaYJ9e0aU+k9ARfegxwmbFvjfKOWGpfFJOq+ugMw==", "path": "livechartscore.skiasharpview.wpf/2.0.0-rc2", "hashPath": "livechartscore.skiasharpview.wpf.2.0.0-rc2.nupkg.sha512"}, "MahApps.Metro/2.4.9": {"type": "package", "serviceable": true, "sha512": "sha512-eMTkg6TBnCwHzszw7CP+pxsBeB4ZMsJFiTJJoifUVBysRyEenzzR+TKQJuMvvKK6KzvLxwHmJsFhi9o5p4vxhQ==", "path": "mahapps.metro/2.4.9", "hashPath": "mahapps.metro.2.4.9.nupkg.sha512"}, "MaterialDesignColors/2.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-b4tdvxPZUCKCpxeCNFnF/+2Hug2cd+/SUGYCt+RXRRrobFYZkZQsIjlbsTGy5Hup9yPMMxqFSEXGRIxCm2JZUA==", "path": "materialdesigncolors/2.0.9", "hashPath": "materialdesigncolors.2.0.9.nupkg.sha512"}, "MaterialDesignThemes/4.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-Yx3RxIqkwoe1rYxoUZ2iH1YdCly+dA8YAQJ5uknsUVhyVd1rgiXEn6fmtsovGaqKRZwUXGHnh4w7kRagxa8TCg==", "path": "materialdesignthemes/4.6.1", "hashPath": "materialdesignthemes.4.6.1.nupkg.sha512"}, "Microsoft.Xaml.Behaviors.Wpf/1.1.19": {"type": "package", "serviceable": true, "sha512": "sha512-5sPWkbqImc2t1aQwIfJcKsUo7tOg1Tr8+6xVzZJB56Nzt4u9NlpcLofgdX/aRYpPKdWDA3U23Akw1KQzU5e82g==", "path": "microsoft.xaml.behaviors.wpf/1.1.19", "hashPath": "microsoft.xaml.behaviors.wpf.1.1.19.nupkg.sha512"}, "NLog/5.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-hMyC7jq1m7c339DZXTRz96b+0YsIYSwAUB8sAWMtqXd2uwJjYe+y1k74fbItODPqDuE/krd6A4mxew9gLMS+PQ==", "path": "nlog/5.0.4", "hashPath": "nlog.5.0.4.nupkg.sha512"}, "Prism.Core/8.1.97": {"type": "package", "serviceable": true, "sha512": "sha512-EP5zrvWddw3eSq25Y7hHnDYdmLZEC2Z/gMrvmHzUuLbitmA1UaS7wQUlSwNr9Km8lzJNCvytFnaGBEFukHgoHg==", "path": "prism.core/8.1.97", "hashPath": "prism.core.8.1.97.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "SkiaSharp/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-wdfeBAQrEQCbJIRgAiargzP1Uy+0grZiG4CSgBnhAgcJTsPzlifIaO73JRdwIlT3TyBoeU9jEqzwFUhl4hTYnQ==", "path": "skiasharp/2.88.6", "hashPath": "skiasharp.2.88.6.nupkg.sha512"}, "SkiaSharp.HarfBuzz/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-s5yZTHdqbXKiTL06ns6zW3asELfX60dEceA4ZdYmNlOkz/OyWDfdjHAuu4HGDA7Mxx5iaUEzDZgPkEe+OVr/jg==", "path": "skiasharp.harfbuzz/2.88.6", "hashPath": "skiasharp.harfbuzz.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-Sko9LFxRXSjb3OGh5/RxrVRXxYo48tr5NKuuSy6jB85GrYt8WRqVY1iLOLwtjPiVAt4cp+pyD4i30azanS64dw==", "path": "skiasharp.nativeassets.macos/2.88.6", "hashPath": "skiasharp.nativeassets.macos.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-7TzFO0u/g2MpQsTty4fyCDdMcfcWI+aLswwfnYXr3gtNS6VLKdMXPMeKpJa3pJSLnUBN6wD0JjuCe8OoLBQ6cQ==", "path": "skiasharp.nativeassets.win32/2.88.6", "hashPath": "skiasharp.nativeassets.win32.2.88.6.nupkg.sha512"}, "SkiaSharp.Views.Desktop.Common/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-Ectb9s9+xH8zh82UbagYX4l6bdR1PcWz7xmaVzcyBcmVhrDmty61U0vIlXgthZH0wJ9fmFX3X+71OwrA9c9adw==", "path": "skiasharp.views.desktop.common/2.88.6", "hashPath": "skiasharp.views.desktop.common.2.88.6.nupkg.sha512"}, "SkiaSharp.Views.WPF/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-JQFZ8BeEg7Lv4uMMbgGnbq7GLi9w63mvL+tzkMzFYmXBEDuFibubWPQmYwOkEo5dlWee0Wn7K1SBhMb8EMFROg==", "path": "skiasharp.views.wpf/2.88.6", "hashPath": "skiasharp.views.wpf.2.88.6.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.116": {"type": "package", "serviceable": true, "sha512": "sha512-VEe/vzvn2ECKovtyb+nf8fsnNQ4EYOUms7gnp9729NN7FSR/0v/uVoBDO7DETzV7YCquRVwRnSnYuoOwAoTEKA==", "path": "stub.system.data.sqlite.core.netstandard/1.0.116", "hashPath": "stub.system.data.sqlite.core.netstandard.1.0.116.nupkg.sha512"}, "System.Data.SqlClient/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-HKLykcv6eZLbLnSMnlQ6Os4+UAmFE+AgYm92CTvJYeTOBtOYusX3qu8OoGhFrnKZax91UcLcDo5vPrqvJUTSNQ==", "path": "system.data.sqlclient/4.8.1", "hashPath": "system.data.sqlclient.4.8.1.nupkg.sha512"}, "System.Data.SQLite/1.0.116": {"type": "package", "serviceable": true, "sha512": "sha512-ykbwLhE8ejsQKMGb1xUCVsbMboNG1BWEr88fdxCW6t3yoIFqGpYpkSpskXw7IACveAFf7FDNuYRSqNIqBlZFMg==", "path": "system.data.sqlite/1.0.116", "hashPath": "system.data.sqlite.1.0.116.nupkg.sha512"}, "System.Data.SQLite.Core/1.0.116": {"type": "package", "serviceable": true, "sha512": "sha512-djwmo97syWCpUPJbDS0e2qUFUa8cDLeIfMotVkaRkAC5gpfBSZLMzvoLkLp1prY8waAuH1jEC3wcB2ymVVQWtA==", "path": "system.data.sqlite.core/1.0.116", "hashPath": "system.data.sqlite.core.1.0.116.nupkg.sha512"}, "System.Data.SQLite.EF6/1.0.116": {"type": "package", "serviceable": true, "sha512": "sha512-oWkl3W9ZKb4oiS4mFd363qK317t/l5KyM3dmj7BD+eW7Qji8Oid5L4Tn+OshQrgY7xNpG4Ga9NUZVXyF91qXAA==", "path": "system.data.sqlite.ef6/1.0.116", "hashPath": "system.data.sqlite.ef6.1.0.116.nupkg.sha512"}}}