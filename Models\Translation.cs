using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    public class Translation : INotifyPropertyChanged
    {
        private int _id;
        private string _languageCode;
        private string _key;
        private string _value;
        private string _createdAt;
        private string _updatedAt;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string LanguageCode
        {
            get => _languageCode;
            set
            {
                if (_languageCode != value)
                {
                    _languageCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Key
        {
            get => _key;
            set
            {
                if (_key != value)
                {
                    _key = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    _value = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        // Calculated properties
        public string FormattedCreatedAt
        {
            get
            {
                if (string.IsNullOrEmpty(CreatedAt))
                    return string.Empty;

                if (DateTime.TryParse(CreatedAt, out DateTime date))
                    return date.ToString("yyyy-MM-dd HH:mm:ss");

                return CreatedAt;
            }
        }

        public string FormattedUpdatedAt
        {
            get
            {
                if (string.IsNullOrEmpty(UpdatedAt))
                    return string.Empty;

                if (DateTime.TryParse(UpdatedAt, out DateTime date))
                    return date.ToString("yyyy-MM-dd HH:mm:ss");

                return UpdatedAt;
            }
        }

        // Display properties
        public string DisplayLanguage
        {
            get
            {
                switch (LanguageCode)
                {
                    case "en-US":
                        return "English (US)";
                    case "ar-SA":
                        return "Arabic (Saudi Arabia)";
                    default:
                        return LanguageCode;
                }
            }
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    public class TranslationValidator : AbstractValidator<Translation>
    {
        public TranslationValidator()
        {
            RuleFor(t => t.LanguageCode)
                .NotEmpty().WithMessage("Language code is required")
                .MaximumLength(10).WithMessage("Language code cannot exceed 10 characters");

            RuleFor(t => t.Key)
                .NotEmpty().WithMessage("Translation key is required")
                .MaximumLength(100).WithMessage("Translation key cannot exceed 100 characters");

            RuleFor(t => t.Value)
                .NotNull().WithMessage("Translation value is required");
        }
    }
}