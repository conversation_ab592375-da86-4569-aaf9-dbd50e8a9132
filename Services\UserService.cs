using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class UserService
    {
        private readonly DatabaseService _dbService;

        public UserService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            const string sql = "SELECT Id, Username, Email, FullName, Role, IsActive, LastLogin, CreatedAt, UpdatedAt FROM Users";
            return await _dbService.QueryAsync<User>(sql);
        }

        public async Task<User> GetUserByIdAsync(int id)
        {
            const string sql = "SELECT Id, Username, Email, FullName, Role, IsActive, LastLogin, CreatedAt, UpdatedAt FROM Users WHERE Id = @Id";
            return await _dbService.QuerySingleOrDefaultAsync<User>(sql, new { Id = id });
        }

        public async Task<User> GetUserByUsernameAsync(string username)
        {
            const string sql = "SELECT Id, Username, Email, FullName, Role, IsActive, LastLogin, CreatedAt, UpdatedAt FROM Users WHERE Username = @Username";
            return await _dbService.QuerySingleOrDefaultAsync<User>(sql, new { Username = username });
        }

        public async Task<User> AuthenticateAsync(string username, string password)
        {
            // Get user with password hash for authentication
            const string sql = "SELECT * FROM Users WHERE Username = @Username AND IsActive = 1";
            var user = await _dbService.QuerySingleOrDefaultAsync<User>(sql, new { Username = username });

            if (user == null)
            {
                return null; // User not found or not active
            }

            // Verify password
            if (!VerifyPasswordHash(password, user.PasswordHash, user.PasswordSalt))
            {
                return null; // Password incorrect
            }

            // Update last login time
            await UpdateLastLoginAsync(user.Id);

            // Remove password hash and salt before returning
            user.PasswordHash = null;
            user.PasswordSalt = null;

            return user;
        }

        public async Task<User> CreateUserAsync(User user, string password)
        {
            // Check if username already exists
            if (await GetUserByUsernameAsync(user.Username) != null)
            {
                throw new InvalidOperationException("Username already exists");
            }

            // Create password hash
            CreatePasswordHash(password, out byte[] passwordHash, out byte[] passwordSalt);
            user.PasswordHash = passwordHash;
            user.PasswordSalt = passwordSalt;

            // Set default values
            user.IsActive = true;
            user.CreatedAt = DateTime.Now;

            // Insert user
            return await _dbService.InsertAsync<User>("Users", user);
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            // Get existing user
            var existingUser = await GetUserByIdAsync(user.Id);
            if (existingUser == null)
            {
                return false;
            }

            // Check if username is being changed and if it already exists
            if (user.Username != existingUser.Username && await GetUserByUsernameAsync(user.Username) != null)
            {
                throw new InvalidOperationException("Username already exists");
            }

            // Set update timestamp
            user.UpdatedAt = DateTime.Now;

            // Update user (excluding password fields)
            const string sql = @"
                UPDATE Users 
                SET Username = @Username, Email = @Email, FullName = @FullName, 
                    Role = @Role, IsActive = @IsActive, UpdatedAt = @UpdatedAt 
                WHERE Id = @Id";

            return await _dbService.ExecuteAsync(sql, new
            {
                user.Id,
                user.Username,
                user.Email,
                user.FullName,
                user.Role,
                user.IsActive,
                user.UpdatedAt
            }) > 0;
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            // Get user with password hash for verification
            const string getSql = "SELECT * FROM Users WHERE Id = @Id";
            var user = await _dbService.QuerySingleOrDefaultAsync<User>(getSql, new { Id = userId });

            if (user == null)
            {
                return false;
            }

            // Verify current password
            if (!VerifyPasswordHash(currentPassword, user.PasswordHash, user.PasswordSalt))
            {
                return false; // Current password is incorrect
            }

            // Create new password hash
            CreatePasswordHash(newPassword, out byte[] passwordHash, out byte[] passwordSalt);

            // Update password
            const string updateSql = @"
                UPDATE Users 
                SET PasswordHash = @PasswordHash, PasswordSalt = @PasswordSalt, UpdatedAt = @UpdatedAt 
                WHERE Id = @Id";

            return await _dbService.ExecuteAsync(updateSql, new
            {
                Id = userId,
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            }) > 0;
        }

        public async Task<bool> ResetPasswordAsync(int userId, string newPassword)
        {
            // Create new password hash
            CreatePasswordHash(newPassword, out byte[] passwordHash, out byte[] passwordSalt);

            // Update password
            const string sql = @"
                UPDATE Users 
                SET PasswordHash = @PasswordHash, PasswordSalt = @PasswordSalt, UpdatedAt = @UpdatedAt 
                WHERE Id = @Id";

            return await _dbService.ExecuteAsync(sql, new
            {
                Id = userId,
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            }) > 0;
        }

        public async Task<bool> DeleteUserAsync(int id)
        {
            return await _dbService.DeleteAsync("Users", id);
        }

        public async Task<bool> UpdateLastLoginAsync(int userId)
        {
            const string sql = "UPDATE Users SET LastLogin = @LastLogin WHERE Id = @Id";
            return await _dbService.ExecuteAsync(sql, new
            {
                Id = userId,
                LastLogin = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            }) > 0;
        }

        public async Task<bool> ActivateUserAsync(int userId, bool isActive)
        {
            const string sql = "UPDATE Users SET IsActive = @IsActive, UpdatedAt = @UpdatedAt WHERE Id = @Id";
            return await _dbService.ExecuteAsync(sql, new
            {
                Id = userId,
                IsActive = isActive,
                UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            }) > 0;
        }

        #region Password Hashing

        private static void CreatePasswordHash(string password, out byte[] passwordHash, out byte[] passwordSalt)
        {
            using (var hmac = new HMACSHA512())
            {
                passwordSalt = hmac.Key;
                passwordHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(password));
            }
        }

        private static bool VerifyPasswordHash(string password, byte[] storedHash, byte[] storedSalt)
        {
            if (storedHash == null || storedSalt == null)
            {
                return false;
            }

            using (var hmac = new HMACSHA512(storedSalt))
            {
                var computedHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(password));
                for (int i = 0; i < computedHash.Length; i++)
                {
                    if (computedHash[i] != storedHash[i])
                    {
                        return false;
                    }
                }
            }

            return true;
        }

        #endregion
    }
}