{"version": 3, "targets": {"net6.0-windows7.0": {"ControlzEx/4.4.0": {"type": "package", "dependencies": {"Microsoft.Xaml.Behaviors.Wpf": "1.1.19"}, "compile": {"lib/netcoreapp3.1/ControlzEx.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/ControlzEx.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Dapper/2.0.123": {"type": "package", "compile": {"lib/net5.0/Dapper.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Dapper.dll": {"related": ".xml"}}}, "EntityFramework/6.4.4": {"type": "package", "dependencies": {"System.Data.SqlClient": "4.8.1"}, "compile": {"lib/netstandard2.1/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/netstandard2.1/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "runtime": {"lib/netstandard2.1/EntityFramework.SqlServer.dll": {"related": ".xml"}, "lib/netstandard2.1/EntityFramework.dll": {"related": ".SqlServer.xml;.xml"}}, "build": {"buildTransitive/netcoreapp3.0/EntityFramework.props": {}, "buildTransitive/netcoreapp3.0/EntityFramework.targets": {}}}, "FluentValidation/11.2.2": {"type": "package", "compile": {"lib/net6.0/FluentValidation.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/FluentValidation.dll": {"related": ".xml"}}}, "HarfBuzzSharp/7.3.0": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0"}, "compile": {"lib/net6.0/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "LiveChartsCore/2.0.0-rc2": {"type": "package", "compile": {"lib/net6.0/LiveChartsCore.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/LiveChartsCore.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView/2.0.0-rc2": {"type": "package", "dependencies": {"LiveChartsCore": "2.0.0-rc2", "SkiaSharp": "2.88.6", "SkiaSharp.HarfBuzz": "2.88.6"}, "compile": {"lib/net6.0/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView.WPF/2.0.0-rc2": {"type": "package", "dependencies": {"LiveChartsCore.SkiaSharpView": "2.0.0-rc2", "SkiaSharp.Views.WPF": "2.88.6"}, "compile": {"lib/netcoreapp3.1/LiveChartsCore.SkiaSharpView.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/LiveChartsCore.SkiaSharpView.WPF.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro/2.4.9": {"type": "package", "dependencies": {"ControlzEx": "[4.4.0, 6.0.0)"}, "compile": {"lib/netcoreapp3.1/MahApps.Metro.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/MahApps.Metro.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"], "resource": {"lib/netcoreapp3.1/de/MahApps.Metro.resources.dll": {"locale": "de"}}}, "MaterialDesignColors/2.0.9": {"type": "package", "compile": {"lib/net6.0/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net6.0/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/4.6.1": {"type": "package", "dependencies": {"MaterialDesignColors": "[2.0.9, 3.0.0)"}, "compile": {"lib/net6.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.19": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "NLog/5.0.4": {"type": "package", "compile": {"lib/netstandard2.0/NLog.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NLog.dll": {"related": ".xml"}}}, "Prism.Core/8.1.97": {"type": "package", "compile": {"lib/net5.0/Prism.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Prism.dll": {"related": ".pdb;.xml"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"assetType": "native", "rid": "win-arm64"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"assetType": "native", "rid": "win-x64"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"assetType": "native", "rid": "win-x86"}}}, "SkiaSharp/2.88.6": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.6", "SkiaSharp.NativeAssets.macOS": "2.88.6"}, "compile": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.HarfBuzz/2.88.6": {"type": "package", "dependencies": {"HarfBuzzSharp": "7.3.0", "SkiaSharp": "2.88.6"}, "compile": {"lib/net6.0/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "SkiaSharp.Views.Desktop.Common/2.88.6": {"type": "package", "dependencies": {"SkiaSharp": "2.88.6"}, "compile": {"lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.Views.WPF/2.88.6": {"type": "package", "dependencies": {"SkiaSharp": "2.88.6", "SkiaSharp.Views.Desktop.Common": "2.88.6"}, "compile": {"lib/netcoreapp3.1/SkiaSharp.Views.WPF.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/SkiaSharp.Views.WPF.dll": {"related": ".pdb;.xml"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.116": {"type": "package", "compile": {"lib/netstandard2.1/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.Data.SqlClient/4.8.1": {"type": "package", "dependencies": {"runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "compile": {"ref/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Data.SQLite/1.0.116": {"type": "package", "dependencies": {"System.Data.SQLite.Core": "[1.0.116]", "System.Data.SQLite.EF6": "[1.0.116]"}}, "System.Data.SQLite.Core/1.0.116": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "[1.0.116]"}}, "System.Data.SQLite.EF6/1.0.116": {"type": "package", "dependencies": {"EntityFramework": "6.4.4"}, "compile": {"lib/netstandard2.1/System.Data.SQLite.EF6.dll": {}}, "runtime": {"lib/netstandard2.1/System.Data.SQLite.EF6.dll": {}}}}}, "libraries": {"ControlzEx/4.4.0": {"sha512": "pZ5z4hYWwE4R13UMCVs6vII//nL7hz+Nwn4oJlnsZJRGqJNy6Z9KnJiTZfly6lKFu0pMc1aWBZpx+VqFTQKP1Q==", "type": "package", "path": "controlzex/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "controlzex.4.4.0.nupkg.sha512", "controlzex.nuspec", "lib/net45/ControlzEx.dll", "lib/net45/ControlzEx.pdb", "lib/net45/ControlzEx.xml", "lib/net462/ControlzEx.dll", "lib/net462/ControlzEx.pdb", "lib/net462/ControlzEx.xml", "lib/netcoreapp3.0/ControlzEx.dll", "lib/netcoreapp3.0/ControlzEx.pdb", "lib/netcoreapp3.0/ControlzEx.xml", "lib/netcoreapp3.1/ControlzEx.dll", "lib/netcoreapp3.1/ControlzEx.pdb", "lib/netcoreapp3.1/ControlzEx.xml", "logo-mini.png"]}, "Dapper/2.0.123": {"sha512": "RDFF4rBLLmbpi6pwkY7q/M6UXHRJEOerplDGE5jwEkP/JGJnBauAClYavNKJPW1yOTWRPIyfj4is3EaJxQXILQ==", "type": "package", "path": "dapper/2.0.123", "files": [".nupkg.metadata", ".signature.p7s", "Dapper.png", "dapper.2.0.123.nupkg.sha512", "dapper.nuspec", "lib/net461/Dapper.dll", "lib/net461/Dapper.xml", "lib/net5.0/Dapper.dll", "lib/net5.0/Dapper.xml", "lib/netstandard2.0/Dapper.dll", "lib/netstandard2.0/Dapper.xml"]}, "EntityFramework/6.4.4": {"sha512": "yj1+/4tci7Panu3jKDHYizxwVm0Jvm7b7m057b5h4u8NUHGCR8WIWirBTw+8EptRffwftIWPBeIRGNKD1ewEMQ==", "type": "package", "path": "entityframework/6.4.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/EntityFramework.DefaultItems.props", "build/EntityFramework.props", "build/EntityFramework.targets", "build/Microsoft.Data.Entity.Build.Tasks.dll", "build/netcoreapp3.0/EntityFramework.props", "build/netcoreapp3.0/EntityFramework.targets", "buildTransitive/EntityFramework.props", "buildTransitive/EntityFramework.targets", "buildTransitive/netcoreapp3.0/EntityFramework.props", "buildTransitive/netcoreapp3.0/EntityFramework.targets", "content/net40/App.config.install.xdt", "content/net40/App.config.transform", "content/net40/Web.config.install.xdt", "content/net40/Web.config.transform", "entityframework.6.4.4.nupkg.sha512", "entityframework.nuspec", "lib/net40/EntityFramework.SqlServer.dll", "lib/net40/EntityFramework.SqlServer.xml", "lib/net40/EntityFramework.dll", "lib/net40/EntityFramework.xml", "lib/net45/EntityFramework.SqlServer.dll", "lib/net45/EntityFramework.SqlServer.xml", "lib/net45/EntityFramework.dll", "lib/net45/EntityFramework.xml", "lib/netstandard2.1/EntityFramework.SqlServer.dll", "lib/netstandard2.1/EntityFramework.SqlServer.xml", "lib/netstandard2.1/EntityFramework.dll", "lib/netstandard2.1/EntityFramework.xml", "tools/EntityFramework6.PS2.psd1", "tools/EntityFramework6.PS2.psm1", "tools/EntityFramework6.psd1", "tools/EntityFramework6.psm1", "tools/about_EntityFramework6.help.txt", "tools/init.ps1", "tools/install.ps1", "tools/net40/any/ef6.exe", "tools/net40/any/ef6.pdb", "tools/net40/win-x86/ef6.exe", "tools/net40/win-x86/ef6.pdb", "tools/net45/any/ef6.exe", "tools/net45/any/ef6.pdb", "tools/net45/win-x86/ef6.exe", "tools/net45/win-x86/ef6.pdb", "tools/netcoreapp3.0/any/ef6.dll", "tools/netcoreapp3.0/any/ef6.pdb", "tools/netcoreapp3.0/any/ef6.runtimeconfig.json"]}, "FluentValidation/11.2.2": {"sha512": "5QEte2BUZZF6Sezeekr7Zu1THfgpxU7pmuL2SjIrajuGMah+pxPGiRiLtw+x/tSUSSVmcv9LrUe41lKu4l0n4A==", "type": "package", "path": "fluentvalidation/11.2.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.11.2.2.nupkg.sha512", "fluentvalidation.nuspec", "lib/net5.0/FluentValidation.dll", "lib/net5.0/FluentValidation.xml", "lib/net6.0/FluentValidation.dll", "lib/net6.0/FluentValidation.xml", "lib/netstandard2.0/FluentValidation.dll", "lib/netstandard2.0/FluentValidation.xml", "lib/netstandard2.1/FluentValidation.dll", "lib/netstandard2.1/FluentValidation.xml"]}, "HarfBuzzSharp/7.3.0": {"sha512": "OrQLaxtZMIeS2yHSUtsKzeSdk9CPaCpyJ/JCs+wLfRGatjE8MLUS6LGj6vdbGRxqRavcXs79C9O3oWe6FJR0JQ==", "type": "package", "path": "harfbuzzsharp/7.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "harfbuzzsharp.7.3.0.nupkg.sha512", "harfbuzzsharp.nuspec", "lib/monoandroid1.0/HarfBuzzSharp.dll", "lib/monoandroid1.0/HarfBuzzSharp.pdb", "lib/monoandroid1.0/HarfBuzzSharp.xml", "lib/net462/HarfBuzzSharp.dll", "lib/net462/HarfBuzzSharp.pdb", "lib/net462/HarfBuzzSharp.xml", "lib/net6.0-android30.0/HarfBuzzSharp.dll", "lib/net6.0-android30.0/HarfBuzzSharp.pdb", "lib/net6.0-android30.0/HarfBuzzSharp.xml", "lib/net6.0-ios13.6/HarfBuzzSharp.dll", "lib/net6.0-ios13.6/HarfBuzzSharp.pdb", "lib/net6.0-ios13.6/HarfBuzzSharp.xml", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.dll", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.pdb", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.xml", "lib/net6.0-macos10.15/HarfBuzzSharp.dll", "lib/net6.0-macos10.15/HarfBuzzSharp.pdb", "lib/net6.0-macos10.15/HarfBuzzSharp.xml", "lib/net6.0-tvos13.4/HarfBuzzSharp.dll", "lib/net6.0-tvos13.4/HarfBuzzSharp.pdb", "lib/net6.0-tvos13.4/HarfBuzzSharp.xml", "lib/net6.0/HarfBuzzSharp.dll", "lib/net6.0/HarfBuzzSharp.pdb", "lib/net6.0/HarfBuzzSharp.xml", "lib/netcoreapp3.1/HarfBuzzSharp.dll", "lib/netcoreapp3.1/HarfBuzzSharp.pdb", "lib/netcoreapp3.1/HarfBuzzSharp.xml", "lib/netstandard1.3/HarfBuzzSharp.dll", "lib/netstandard1.3/HarfBuzzSharp.pdb", "lib/netstandard1.3/HarfBuzzSharp.xml", "lib/netstandard2.0/HarfBuzzSharp.dll", "lib/netstandard2.0/HarfBuzzSharp.pdb", "lib/netstandard2.0/HarfBuzzSharp.xml", "lib/netstandard2.1/HarfBuzzSharp.dll", "lib/netstandard2.1/HarfBuzzSharp.pdb", "lib/netstandard2.1/HarfBuzzSharp.xml", "lib/tizen40/HarfBuzzSharp.dll", "lib/tizen40/HarfBuzzSharp.pdb", "lib/tizen40/HarfBuzzSharp.xml", "lib/uap10.0.10240/HarfBuzzSharp.dll", "lib/uap10.0.10240/HarfBuzzSharp.pdb", "lib/uap10.0.10240/HarfBuzzSharp.xml", "lib/uap10.0.16299/HarfBuzzSharp.dll", "lib/uap10.0.16299/HarfBuzzSharp.pdb", "lib/uap10.0.16299/HarfBuzzSharp.xml", "lib/xamarinios1.0/HarfBuzzSharp.dll", "lib/xamarinios1.0/HarfBuzzSharp.pdb", "lib/xamarinios1.0/HarfBuzzSharp.xml", "lib/xamarinmac2.0/HarfBuzzSharp.dll", "lib/xamarinmac2.0/HarfBuzzSharp.pdb", "lib/xamarinmac2.0/HarfBuzzSharp.xml", "lib/xamarintvos1.0/HarfBuzzSharp.dll", "lib/xamarintvos1.0/HarfBuzzSharp.pdb", "lib/xamarintvos1.0/HarfBuzzSharp.xml", "lib/xamarinwatchos1.0/HarfBuzzSharp.dll", "lib/xamarinwatchos1.0/HarfBuzzSharp.pdb", "lib/xamarinwatchos1.0/HarfBuzzSharp.xml"]}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0": {"sha512": "LWcFJ39j+dN0KK8c/GJJZPPZPL9TqT2FA42/LRGqzUMmSm5LYbINOMnPvUr7RuLR6RFSmKIrgrlgObR8G5ho2A==", "type": "package", "path": "harfbuzzsharp.nativeassets.macos/7.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "harfbuzzsharp.nativeassets.macos.7.3.0.nupkg.sha512", "harfbuzzsharp.nativeassets.macos.nuspec", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libHarfBuzzSharp.dylib"]}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0": {"sha512": "ulEewLMk+dNmbmpy15ny/YusI6JNUWqchF080TV2jgfFBXPXjWm767JleDi/S7hp8eDeEN6GYIIxpvNr5fLvIw==", "type": "package", "path": "harfbuzzsharp.nativeassets.win32/7.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "harfbuzzsharp.nativeassets.win32.7.3.0.nupkg.sha512", "harfbuzzsharp.nativeassets.win32.nuspec", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libHarfBuzzSharp.dll", "runtimes/win-x64/native/libHarfBuzzSharp.dll", "runtimes/win-x86/native/libHarfBuzzSharp.dll"]}, "LiveChartsCore/2.0.0-rc2": {"sha512": "X55dG3oI3AVOGWUrly+J/bUJGmb/pmB2aTkaWJPyIXI2cr5cqPJG/6GLL17IJ8e3GOofFWgQCbg0Az0+myUQLg==", "type": "package", "path": "livechartscore/2.0.0-rc2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net6.0-android31.0/LiveChartsCore.dll", "lib/net6.0-android31.0/LiveChartsCore.xml", "lib/net6.0-ios16.1/LiveChartsCore.dll", "lib/net6.0-ios16.1/LiveChartsCore.xml", "lib/net6.0-maccatalyst16.1/LiveChartsCore.dll", "lib/net6.0-maccatalyst16.1/LiveChartsCore.xml", "lib/net6.0/LiveChartsCore.dll", "lib/net6.0/LiveChartsCore.xml", "lib/netcoreapp2.0/LiveChartsCore.dll", "lib/netcoreapp2.0/LiveChartsCore.xml", "lib/netstandard2.0/LiveChartsCore.dll", "lib/netstandard2.0/LiveChartsCore.xml", "livechartscore.2.0.0-rc2.nupkg.sha512", "livechartscore.nuspec"]}, "LiveChartsCore.SkiaSharpView/2.0.0-rc2": {"sha512": "UVvUa7QAlyiPLuCjcoMKr4dI1FIUE5xW/XjcioSXgsOoqZg0E6Fq3B9k5t6MjIVE6dUU2F3tZwdm5KDiNftQ2A==", "type": "package", "path": "livechartscore.skiasharpview/2.0.0-rc2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net6.0-android31.0/LiveChartsCore.SkiaSharpView.dll", "lib/net6.0-android31.0/LiveChartsCore.SkiaSharpView.xml", "lib/net6.0-ios16.1/LiveChartsCore.SkiaSharpView.dll", "lib/net6.0-ios16.1/LiveChartsCore.SkiaSharpView.xml", "lib/net6.0-maccatalyst16.1/LiveChartsCore.SkiaSharpView.dll", "lib/net6.0-maccatalyst16.1/LiveChartsCore.SkiaSharpView.xml", "lib/net6.0/LiveChartsCore.SkiaSharpView.dll", "lib/net6.0/LiveChartsCore.SkiaSharpView.xml", "lib/netcoreapp2.0/LiveChartsCore.SkiaSharpView.dll", "lib/netcoreapp2.0/LiveChartsCore.SkiaSharpView.xml", "lib/netstandard2.0/LiveChartsCore.SkiaSharpView.dll", "lib/netstandard2.0/LiveChartsCore.SkiaSharpView.xml", "livechartscore.skiasharpview.2.0.0-rc2.nupkg.sha512", "livechartscore.skiasharpview.nuspec"]}, "LiveChartsCore.SkiaSharpView.WPF/2.0.0-rc2": {"sha512": "FUpPKrCySv3oP/eilTN7ncHKGgacRwh2MK0CHzMEEAZ+77qaYJ9e0aU+k9ARfegxwmbFvjfKOWGpfFJOq+ugMw==", "type": "package", "path": "livechartscore.skiasharpview.wpf/2.0.0-rc2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net462/LiveChartsCore.SkiaSharpView.WPF.dll", "lib/net462/LiveChartsCore.SkiaSharpView.WPF.xml", "lib/netcoreapp3.1/LiveChartsCore.SkiaSharpView.WPF.dll", "lib/netcoreapp3.1/LiveChartsCore.SkiaSharpView.WPF.xml", "livechartscore.skiasharpview.wpf.2.0.0-rc2.nupkg.sha512", "livechartscore.skiasharpview.wpf.nuspec"]}, "MahApps.Metro/2.4.9": {"sha512": "eMTkg6TBnCwHzszw7CP+pxsBeB4ZMsJFiTJJoifUVBysRyEenzzR+TKQJuMvvKK6KzvLxwHmJsFhi9o5p4vxhQ==", "type": "package", "path": "mahapps.metro/2.4.9", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/MahApps.Metro.dll", "lib/net452/MahApps.Metro.pdb", "lib/net452/MahApps.Metro.xml", "lib/net452/de/MahApps.Metro.resources.dll", "lib/net46/MahApps.Metro.dll", "lib/net46/MahApps.Metro.pdb", "lib/net46/MahApps.Metro.xml", "lib/net46/de/MahApps.Metro.resources.dll", "lib/net47/MahApps.Metro.dll", "lib/net47/MahApps.Metro.pdb", "lib/net47/MahApps.Metro.xml", "lib/net47/de/MahApps.Metro.resources.dll", "lib/netcoreapp3.0/MahApps.Metro.dll", "lib/netcoreapp3.0/MahApps.Metro.pdb", "lib/netcoreapp3.0/MahApps.Metro.xml", "lib/netcoreapp3.0/de/MahApps.Metro.resources.dll", "lib/netcoreapp3.1/MahApps.Metro.dll", "lib/netcoreapp3.1/MahApps.Metro.pdb", "lib/netcoreapp3.1/MahApps.Metro.xml", "lib/netcoreapp3.1/de/MahApps.Metro.resources.dll", "mahapps.metro.2.4.9.nupkg.sha512", "mahapps.metro.logo.png", "mahapps.metro.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "MaterialDesignColors/2.0.9": {"sha512": "b4tdvxPZUCKCpxeCNFnF/+2Hug2cd+/SUGYCt+RXRRrobFYZkZQsIjlbsTGy5Hup9yPMMxqFSEXGRIxCm2JZUA==", "type": "package", "path": "materialdesigncolors/2.0.9", "files": [".nupkg.metadata", ".signature.p7s", "images/MaterialDesignColors.Icon.png", "lib/net462/MaterialDesignColors.dll", "lib/net462/MaterialDesignColors.pdb", "lib/net6.0/MaterialDesignColors.dll", "lib/net6.0/MaterialDesignColors.pdb", "lib/netcoreapp3.1/MaterialDesignColors.dll", "lib/netcoreapp3.1/MaterialDesignColors.pdb", "materialdesigncolors.2.0.9.nupkg.sha512", "materialdesigncolors.nuspec"]}, "MaterialDesignThemes/4.6.1": {"sha512": "Yx3RxIqkwoe1rYxoUZ2iH1YdCly+dA8YAQJ5uknsUVhyVd1rgiXEn6fmtsovGaqKRZwUXGHnh4w7kRagxa8TCg==", "type": "package", "path": "materialdesignthemes/4.6.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/MaterialDesignThemes.targets", "build/Resources/Roboto/Roboto-Black.ttf", "build/Resources/Roboto/Roboto-BlackItalic.ttf", "build/Resources/Roboto/Roboto-Bold.ttf", "build/Resources/Roboto/Roboto-BoldItalic.ttf", "build/Resources/Roboto/Roboto-Italic.ttf", "build/Resources/Roboto/Roboto-Light.ttf", "build/Resources/Roboto/Roboto-LightItalic.ttf", "build/Resources/Roboto/Roboto-Medium.ttf", "build/Resources/Roboto/Roboto-MediumItalic.ttf", "build/Resources/Roboto/Roboto-Regular.ttf", "build/Resources/Roboto/Roboto-Thin.ttf", "build/Resources/Roboto/Roboto-ThinItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Bold.ttf", "build/Resources/Roboto/RobotoCondensed-BoldItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Italic.ttf", "build/Resources/Roboto/RobotoCondensed-Light.ttf", "build/Resources/Roboto/RobotoCondensed-LightItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Regular.ttf", "images/MaterialDesignThemes.Icon.png", "lib/net462/MaterialDesignThemes.Wpf.dll", "lib/net462/MaterialDesignThemes.Wpf.pdb", "lib/net462/MaterialDesignThemes.Wpf.xml", "lib/net6.0/MaterialDesignThemes.Wpf.dll", "lib/net6.0/MaterialDesignThemes.Wpf.pdb", "lib/net6.0/MaterialDesignThemes.Wpf.xml", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.dll", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.pdb", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.xml", "materialdesignthemes.4.6.1.nupkg.sha512", "materialdesignthemes.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.19": {"sha512": "5sPWkbqImc2t1aQwIfJcKsUo7tOg1Tr8+6xVzZJB56Nzt4u9NlpcLofgdX/aRYpPKdWDA3U23Akw1KQzU5e82g==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.19", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net45/Microsoft.Xaml.Behaviors.dll", "lib/net45/Microsoft.Xaml.Behaviors.pdb", "lib/net45/Microsoft.Xaml.Behaviors.xml", "lib/netcoreapp3.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/netcoreapp3.0/Microsoft.Xaml.Behaviors.dll", "lib/netcoreapp3.0/Microsoft.Xaml.Behaviors.pdb", "lib/netcoreapp3.0/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.19.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "NLog/5.0.4": {"sha512": "hMyC7jq1m7c339DZXTRz96b+0YsIYSwAUB8sAWMtqXd2uwJjYe+y1k74fbItODPqDuE/krd6A4mxew9gLMS+PQ==", "type": "package", "path": "nlog/5.0.4", "files": [".nupkg.metadata", ".signature.p7s", "N.png", "lib/net35/NLog.dll", "lib/net35/NLog.xml", "lib/net45/NLog.dll", "lib/net45/NLog.xml", "lib/net46/NLog.dll", "lib/net46/NLog.xml", "lib/netstandard1.3/NLog.dll", "lib/netstandard1.3/NLog.xml", "lib/netstandard1.5/NLog.dll", "lib/netstandard1.5/NLog.xml", "lib/netstandard2.0/NLog.dll", "lib/netstandard2.0/NLog.xml", "nlog.5.0.4.nupkg.sha512", "nlog.nuspec"]}, "Prism.Core/8.1.97": {"sha512": "EP5zrvWddw3eSq25Y7hHnDYdmLZEC2Z/gMrvmHzUuLbitmA1UaS7wQUlSwNr9Km8lzJNCvytFnaGBEFukHgoHg==", "type": "package", "path": "prism.core/8.1.97", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net461/Prism.dll", "lib/net461/Prism.pdb", "lib/net461/Prism.xml", "lib/net47/Prism.dll", "lib/net47/Prism.pdb", "lib/net47/Prism.xml", "lib/net5.0/Prism.dll", "lib/net5.0/Prism.pdb", "lib/net5.0/Prism.xml", "lib/netstandard2.0/Prism.dll", "lib/netstandard2.0/Prism.pdb", "lib/netstandard2.0/Prism.xml", "prism-logo.png", "prism.core.8.1.97.nupkg.sha512", "prism.core.nuspec", "readme.txt"]}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"sha512": "9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "type": "package", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "runtime.native.system.data.sqlclient.sni.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "type": "package", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-arm64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "type": "package", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "type": "package", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x86/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "SkiaSharp/2.88.6": {"sha512": "wdfeBAQrEQCbJIRgAiargzP1Uy+0grZiG4CSgBnhAgcJTsPzlifIaO73JRdwIlT3TyBoeU9jEqzwFUhl4hTYnQ==", "type": "package", "path": "skiasharp/2.88.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/monoandroid1.0/SkiaSharp.dll", "lib/monoandroid1.0/SkiaSharp.pdb", "lib/monoandroid1.0/SkiaSharp.xml", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net462/SkiaSharp.xml", "lib/net6.0-android30.0/SkiaSharp.dll", "lib/net6.0-android30.0/SkiaSharp.pdb", "lib/net6.0-android30.0/SkiaSharp.xml", "lib/net6.0-ios13.6/SkiaSharp.dll", "lib/net6.0-ios13.6/SkiaSharp.pdb", "lib/net6.0-ios13.6/SkiaSharp.xml", "lib/net6.0-maccatalyst13.5/SkiaSharp.dll", "lib/net6.0-maccatalyst13.5/SkiaSharp.pdb", "lib/net6.0-maccatalyst13.5/SkiaSharp.xml", "lib/net6.0-macos10.15/SkiaSharp.dll", "lib/net6.0-macos10.15/SkiaSharp.pdb", "lib/net6.0-macos10.15/SkiaSharp.xml", "lib/net6.0-tizen7.0/SkiaSharp.dll", "lib/net6.0-tizen7.0/SkiaSharp.pdb", "lib/net6.0-tizen7.0/SkiaSharp.xml", "lib/net6.0-tvos13.4/SkiaSharp.dll", "lib/net6.0-tvos13.4/SkiaSharp.pdb", "lib/net6.0-tvos13.4/SkiaSharp.xml", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.xml", "lib/netcoreapp3.1/SkiaSharp.dll", "lib/netcoreapp3.1/SkiaSharp.pdb", "lib/netcoreapp3.1/SkiaSharp.xml", "lib/netstandard1.3/SkiaSharp.dll", "lib/netstandard1.3/SkiaSharp.pdb", "lib/netstandard1.3/SkiaSharp.xml", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.xml", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.xml", "lib/tizen40/SkiaSharp.dll", "lib/tizen40/SkiaSharp.pdb", "lib/tizen40/SkiaSharp.xml", "lib/uap10.0.10240/SkiaSharp.dll", "lib/uap10.0.10240/SkiaSharp.pdb", "lib/uap10.0.10240/SkiaSharp.xml", "lib/uap10.0.16299/SkiaSharp.dll", "lib/uap10.0.16299/SkiaSharp.pdb", "lib/uap10.0.16299/SkiaSharp.xml", "lib/xamarinios1.0/SkiaSharp.dll", "lib/xamarinios1.0/SkiaSharp.pdb", "lib/xamarinios1.0/SkiaSharp.xml", "lib/xamarinmac2.0/SkiaSharp.dll", "lib/xamarinmac2.0/SkiaSharp.pdb", "lib/xamarinmac2.0/SkiaSharp.xml", "lib/xamarintvos1.0/SkiaSharp.dll", "lib/xamarintvos1.0/SkiaSharp.pdb", "lib/xamarintvos1.0/SkiaSharp.xml", "lib/xamarinwatchos1.0/SkiaSharp.dll", "lib/xamarinwatchos1.0/SkiaSharp.pdb", "lib/xamarinwatchos1.0/SkiaSharp.xml", "skiasharp.2.88.6.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.HarfBuzz/2.88.6": {"sha512": "s5yZTHdqbXKiTL06ns6zW3asELfX60dEceA4ZdYmNlOkz/OyWDfdjHAuu4HGDA7Mxx5iaUEzDZgPkEe+OVr/jg==", "type": "package", "path": "skiasharp.harfbuzz/2.88.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net462/SkiaSharp.HarfBuzz.dll", "lib/net462/SkiaSharp.HarfBuzz.pdb", "lib/net462/SkiaSharp.HarfBuzz.xml", "lib/net6.0/SkiaSharp.HarfBuzz.dll", "lib/net6.0/SkiaSharp.HarfBuzz.pdb", "lib/net6.0/SkiaSharp.HarfBuzz.xml", "lib/netcoreapp3.1/SkiaSharp.HarfBuzz.dll", "lib/netcoreapp3.1/SkiaSharp.HarfBuzz.pdb", "lib/netcoreapp3.1/SkiaSharp.HarfBuzz.xml", "lib/netstandard1.3/SkiaSharp.HarfBuzz.dll", "lib/netstandard1.3/SkiaSharp.HarfBuzz.pdb", "lib/netstandard1.3/SkiaSharp.HarfBuzz.xml", "lib/netstandard2.0/SkiaSharp.HarfBuzz.dll", "lib/netstandard2.0/SkiaSharp.HarfBuzz.pdb", "lib/netstandard2.0/SkiaSharp.HarfBuzz.xml", "lib/netstandard2.1/SkiaSharp.HarfBuzz.dll", "lib/netstandard2.1/SkiaSharp.HarfBuzz.pdb", "lib/netstandard2.1/SkiaSharp.HarfBuzz.xml", "skiasharp.harfbuzz.2.88.6.nupkg.sha512", "skiasharp.harfbuzz.nuspec"]}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"sha512": "Sko9LFxRXSjb3OGh5/RxrVRXxYo48tr5NKuuSy6jB85GrYt8WRqVY1iLOLwtjPiVAt4cp+pyD4i30azanS64dw==", "type": "package", "path": "skiasharp.nativeassets.macos/2.88.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.2.88.6.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"sha512": "7TzFO0u/g2MpQsTty4fyCDdMcfcWI+aLswwfnYXr3gtNS6VLKdMXPMeKpJa3pJSLnUBN6wD0JjuCe8OoLBQ6cQ==", "type": "package", "path": "skiasharp.nativeassets.win32/2.88.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.2.88.6.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "SkiaSharp.Views.Desktop.Common/2.88.6": {"sha512": "Ectb9s9+xH8zh82UbagYX4l6bdR1PcWz7xmaVzcyBcmVhrDmty61U0vIlXgthZH0wJ9fmFX3X+71OwrA9c9adw==", "type": "package", "path": "skiasharp.views.desktop.common/2.88.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net462/SkiaSharp.Views.Desktop.Common.dll", "lib/net462/SkiaSharp.Views.Desktop.Common.pdb", "lib/net462/SkiaSharp.Views.Desktop.Common.xml", "lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.dll", "lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.pdb", "lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.xml", "lib/netstandard2.0/SkiaSharp.Views.Desktop.Common.dll", "lib/netstandard2.0/SkiaSharp.Views.Desktop.Common.pdb", "lib/netstandard2.0/SkiaSharp.Views.Desktop.Common.xml", "skiasharp.views.desktop.common.2.88.6.nupkg.sha512", "skiasharp.views.desktop.common.nuspec"]}, "SkiaSharp.Views.WPF/2.88.6": {"sha512": "JQFZ8BeEg7Lv4uMMbgGnbq7GLi9w63mvL+tzkMzFYmXBEDuFibubWPQmYwOkEo5dlWee0Wn7K1SBhMb8EMFROg==", "type": "package", "path": "skiasharp.views.wpf/2.88.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net462/SkiaSharp.Views.WPF.dll", "lib/net462/SkiaSharp.Views.WPF.pdb", "lib/net462/SkiaSharp.Views.WPF.xml", "lib/netcoreapp3.1/SkiaSharp.Views.WPF.dll", "lib/netcoreapp3.1/SkiaSharp.Views.WPF.pdb", "lib/netcoreapp3.1/SkiaSharp.Views.WPF.xml", "skiasharp.views.wpf.2.88.6.nupkg.sha512", "skiasharp.views.wpf.nuspec"]}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.116": {"sha512": "VEe/vzvn2ECKovtyb+nf8fsnNQ4EYOUms7gnp9729NN7FSR/0v/uVoBDO7DETzV7YCquRVwRnSnYuoOwAoTEKA==", "type": "package", "path": "stub.system.data.sqlite.core.netstandard/1.0.116", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/System.Data.SQLite.dll", "lib/netstandard2.0/System.Data.SQLite.dll.altconfig", "lib/netstandard2.0/System.Data.SQLite.xml", "lib/netstandard2.1/System.Data.SQLite.dll", "lib/netstandard2.1/System.Data.SQLite.dll.altconfig", "lib/netstandard2.1/System.Data.SQLite.xml", "runtimes/linux-x64/native/SQLite.Interop.dll", "runtimes/osx-x64/native/SQLite.Interop.dll", "runtimes/win-x64/native/SQLite.Interop.dll", "runtimes/win-x86/native/SQLite.Interop.dll", "stub.system.data.sqlite.core.netstandard.1.0.116.nupkg.sha512", "stub.system.data.sqlite.core.netstandard.nuspec"]}, "System.Data.SqlClient/4.8.1": {"sha512": "HKLykcv6eZLbLnSMnlQ6Os4+UAmFE+AgYm92CTvJYeTOBtOYusX3qu8OoGhFrnKZax91UcLcDo5vPrqvJUTSNQ==", "type": "package", "path": "system.data.sqlclient/4.8.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.SqlClient.dll", "lib/net46/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.xml", "lib/netcoreapp2.1/System.Data.SqlClient.dll", "lib/netcoreapp2.1/System.Data.SqlClient.xml", "lib/netstandard1.2/System.Data.SqlClient.dll", "lib/netstandard1.2/System.Data.SqlClient.xml", "lib/netstandard1.3/System.Data.SqlClient.dll", "lib/netstandard1.3/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.SqlClient.dll", "ref/net46/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.xml", "ref/netcoreapp2.1/System.Data.SqlClient.dll", "ref/netcoreapp2.1/System.Data.SqlClient.xml", "ref/netstandard1.2/System.Data.SqlClient.dll", "ref/netstandard1.2/System.Data.SqlClient.xml", "ref/netstandard1.2/de/System.Data.SqlClient.xml", "ref/netstandard1.2/es/System.Data.SqlClient.xml", "ref/netstandard1.2/fr/System.Data.SqlClient.xml", "ref/netstandard1.2/it/System.Data.SqlClient.xml", "ref/netstandard1.2/ja/System.Data.SqlClient.xml", "ref/netstandard1.2/ko/System.Data.SqlClient.xml", "ref/netstandard1.2/ru/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hant/System.Data.SqlClient.xml", "ref/netstandard1.3/System.Data.SqlClient.dll", "ref/netstandard1.3/System.Data.SqlClient.xml", "ref/netstandard1.3/de/System.Data.SqlClient.xml", "ref/netstandard1.3/es/System.Data.SqlClient.xml", "ref/netstandard1.3/fr/System.Data.SqlClient.xml", "ref/netstandard1.3/it/System.Data.SqlClient.xml", "ref/netstandard1.3/ja/System.Data.SqlClient.xml", "ref/netstandard1.3/ko/System.Data.SqlClient.xml", "ref/netstandard1.3/ru/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hant/System.Data.SqlClient.xml", "ref/netstandard2.0/System.Data.SqlClient.dll", "ref/netstandard2.0/System.Data.SqlClient.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/unix/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/net451/System.Data.SqlClient.dll", "runtimes/win/lib/net46/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.xml", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/win/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.dll", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.xml", "system.data.sqlclient.4.8.1.nupkg.sha512", "system.data.sqlclient.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Data.SQLite/1.0.116": {"sha512": "ykbwLhE8ejsQKMGb1xUCVsbMboNG1BWEr88fdxCW6t3yoIFqGpYpkSpskXw7IACveAFf7FDNuYRSqNIqBlZFMg==", "type": "package", "path": "system.data.sqlite/1.0.116", "files": [".nupkg.metadata", ".signature.p7s", "system.data.sqlite.1.0.116.nupkg.sha512", "system.data.sqlite.nuspec"]}, "System.Data.SQLite.Core/1.0.116": {"sha512": "djwmo97syWCpUPJbDS0e2qUFUa8cDLeIfMotVkaRkAC5gpfBSZLMzvoLkLp1prY8waAuH1jEC3wcB2ymVVQWtA==", "type": "package", "path": "system.data.sqlite.core/1.0.116", "files": [".nupkg.metadata", ".signature.p7s", "system.data.sqlite.core.1.0.116.nupkg.sha512", "system.data.sqlite.core.nuspec"]}, "System.Data.SQLite.EF6/1.0.116": {"sha512": "oWkl3W9ZKb4oiS4mFd363qK317t/l5KyM3dmj7BD+eW7Qji8Oid5L4Tn+OshQrgY7xNpG4Ga9NUZVXyF91qXAA==", "type": "package", "path": "system.data.sqlite.ef6/1.0.116", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "content/net40/app.config.install.xdt", "content/net40/app.config.transform", "content/net40/web.config.install.xdt", "content/net40/web.config.transform", "content/net45/app.config.install.xdt", "content/net45/app.config.transform", "content/net45/web.config.install.xdt", "content/net45/web.config.transform", "content/net451/app.config.install.xdt", "content/net451/app.config.transform", "content/net451/web.config.install.xdt", "content/net451/web.config.transform", "content/net46/app.config.install.xdt", "content/net46/app.config.transform", "content/net46/web.config.install.xdt", "content/net46/web.config.transform", "lib/net40/System.Data.SQLite.EF6.dll", "lib/net45/System.Data.SQLite.EF6.dll", "lib/net451/System.Data.SQLite.EF6.dll", "lib/net46/System.Data.SQLite.EF6.dll", "lib/netstandard2.1/System.Data.SQLite.EF6.dll", "system.data.sqlite.ef6.1.0.116.nupkg.sha512", "system.data.sqlite.ef6.nuspec", "tools/net40/install.ps1", "tools/net45/install.ps1", "tools/net451/install.ps1", "tools/net46/install.ps1"]}}, "projectFileDependencyGroups": {"net6.0-windows7.0": ["Dapper >= 2.0.123", "FluentValidation >= 11.2.2", "LiveChartsCore.SkiaSharpView.WPF >= 2.0.0-rc2", "MahApps.Metro >= 2.4.9", "MaterialDesignThemes >= 4.6.1", "NLog >= 5.0.4", "Prism.Core >= 8.1.97", "System.Data.SQLite >= 1.0.116"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\accountant\\SalesManagementSystem\\SalesManagementSystem\\SalesManagementSystem.csproj", "projectName": "SalesManagementSystem", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\accountant\\SalesManagementSystem\\SalesManagementSystem\\SalesManagementSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\accountant\\SalesManagementSystem\\SalesManagementSystem\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Dapper": {"target": "Package", "version": "[2.0.123, )"}, "FluentValidation": {"target": "Package", "version": "[11.2.2, )"}, "LiveChartsCore.SkiaSharpView.WPF": {"target": "Package", "version": "[2.0.0-rc2, )"}, "MahApps.Metro": {"target": "Package", "version": "[2.4.9, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.6.1, )"}, "NLog": {"target": "Package", "version": "[5.0.4, )"}, "Prism.Core": {"target": "Package", "version": "[8.1.97, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.116, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.4.25258.110\\RuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(, 4.7.32767]", "Microsoft.NETCore.App": "(, 2.1.32767]", "Microsoft.VisualBasic": "(, 10.3.32767]", "Microsoft.Win32.Primitives": "(, 4.3.32767]", "Microsoft.Win32.Registry": "(, 5.0.32767]", "Microsoft.Win32.Registry.AccessControl": "(, 6.0.32767]", "Microsoft.Win32.SystemEvents": "(, 6.0.32767]", "runtime.any.System.Collections": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.any.System.Globalization": "(, 4.3.32767]", "runtime.any.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.any.System.IO": "(, 4.3.32767]", "runtime.any.System.Reflection": "(, 4.3.32767]", "runtime.any.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.any.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.any.System.Runtime": "(, 4.3.32767]", "runtime.any.System.Runtime.Handles": "(, 4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.any.System.Text.Encoding": "(, 4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.any.System.Threading.Tasks": "(, 4.3.32767]", "runtime.any.System.Threading.Timer": "(, 4.3.32767]", "runtime.aot.System.Collections": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.aot.System.Globalization": "(, 4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.aot.System.IO": "(, 4.3.32767]", "runtime.aot.System.Reflection": "(, 4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.aot.System.Runtime": "(, 4.3.32767]", "runtime.aot.System.Runtime.Handles": "(, 4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.aot.System.Threading.Tasks": "(, 4.3.32767]", "runtime.aot.System.Threading.Timer": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.unix.System.Console": "(, 4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.unix.System.IO.FileSystem": "(, 4.3.32767]", "runtime.unix.System.Net.Primitives": "(, 4.3.32767]", "runtime.unix.System.Net.Sockets": "(, 4.3.32767]", "runtime.unix.System.Private.Uri": "(, 4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.win.System.Console": "(, 4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.win.System.IO.FileSystem": "(, 4.3.32767]", "runtime.win.System.Net.Primitives": "(, 4.3.32767]", "runtime.win.System.Net.Sockets": "(, 4.3.32767]", "runtime.win.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7.System.Private.Uri": "(, 4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.32767]", "System.AppContext": "(, 4.3.32767]", "System.Buffers": "(, 4.5.32767]", "System.CodeDom": "(, 6.0.32767]", "System.Collections": "(, 4.3.32767]", "System.Collections.Concurrent": "(, 4.3.32767]", "System.Collections.Immutable": "(, 6.0.32767]", "System.Collections.NonGeneric": "(, 4.3.32767]", "System.Collections.Specialized": "(, 4.3.32767]", "System.ComponentModel": "(, 4.3.32767]", "System.ComponentModel.Annotations": "(, 5.0.32767]", "System.ComponentModel.EventBasedAsync": "(, 4.3.32767]", "System.ComponentModel.Primitives": "(, 4.3.32767]", "System.ComponentModel.TypeConverter": "(, 4.3.32767]", "System.Configuration.ConfigurationManager": "(, 6.0.32767]", "System.Console": "(, 4.3.32767]", "System.Data.Common": "(, 4.3.32767]", "System.Data.DataSetExtensions": "(, 4.5.32767]", "System.Diagnostics.Contracts": "(, 4.3.32767]", "System.Diagnostics.Debug": "(, 4.3.32767]", "System.Diagnostics.DiagnosticSource": "(, 6.0.32767]", "System.Diagnostics.EventLog": "(, 6.0.32767]", "System.Diagnostics.FileVersionInfo": "(, 4.3.32767]", "System.Diagnostics.PerformanceCounter": "(, 6.0.32767]", "System.Diagnostics.Process": "(, 4.3.32767]", "System.Diagnostics.StackTrace": "(, 4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.32767]", "System.Diagnostics.Tools": "(, 4.3.32767]", "System.Diagnostics.TraceSource": "(, 4.3.32767]", "System.Diagnostics.Tracing": "(, 4.3.32767]", "System.DirectoryServices": "(, 8.0.32767]", "System.Drawing.Common": "(, 6.0.32767]", "System.Drawing.Primitives": "(, 4.3.32767]", "System.Dynamic.Runtime": "(, 4.3.32767]", "System.Formats.Asn1": "(, 6.0.32767]", "System.Globalization": "(, 4.3.32767]", "System.Globalization.Calendars": "(, 4.3.32767]", "System.Globalization.Extensions": "(, 4.3.32767]", "System.IO": "(, 4.3.32767]", "System.IO.Compression": "(, 4.3.32767]", "System.IO.Compression.ZipFile": "(, 4.3.32767]", "System.IO.FileSystem": "(, 4.3.32767]", "System.IO.FileSystem.AccessControl": "(, 5.0.32767]", "System.IO.FileSystem.DriveInfo": "(, 4.3.32767]", "System.IO.FileSystem.Primitives": "(, 4.3.32767]", "System.IO.FileSystem.Watcher": "(, 4.3.32767]", "System.IO.IsolatedStorage": "(, 4.3.32767]", "System.IO.MemoryMappedFiles": "(, 4.3.32767]", "System.IO.Packaging": "(, 6.0.32767]", "System.IO.Pipes": "(, 4.3.32767]", "System.IO.Pipes.AccessControl": "(, 5.0.32767]", "System.IO.UnmanagedMemoryStream": "(, 4.3.32767]", "System.Linq": "(, 4.3.32767]", "System.Linq.Expressions": "(, 4.3.32767]", "System.Linq.Parallel": "(, 4.3.32767]", "System.Linq.Queryable": "(, 4.3.32767]", "System.Memory": "(, 4.5.32767]", "System.Net.Http": "(, 4.3.32767]", "System.Net.Http.Json": "(, 6.0.32767]", "System.Net.NameResolution": "(, 4.3.32767]", "System.Net.NetworkInformation": "(, 4.3.32767]", "System.Net.Ping": "(, 4.3.32767]", "System.Net.Primitives": "(, 4.3.32767]", "System.Net.Requests": "(, 4.3.32767]", "System.Net.Security": "(, 4.3.32767]", "System.Net.Sockets": "(, 4.3.32767]", "System.Net.WebHeaderCollection": "(, 4.3.32767]", "System.Net.WebSockets": "(, 4.3.32767]", "System.Net.WebSockets.Client": "(, 4.3.32767]", "System.Numerics.Vectors": "(, 4.5.32767]", "System.ObjectModel": "(, 4.3.32767]", "System.Private.DataContractSerialization": "(, 4.3.32767]", "System.Private.Uri": "(, 4.3.32767]", "System.Reflection": "(, 4.3.32767]", "System.Reflection.DispatchProxy": "(, 4.7.32767]", "System.Reflection.Emit": "(, 4.7.32767]", "System.Reflection.Emit.ILGeneration": "(, 4.7.32767]", "System.Reflection.Emit.Lightweight": "(, 4.7.32767]", "System.Reflection.Extensions": "(, 4.3.32767]", "System.Reflection.Metadata": "(, 6.0.32767]", "System.Reflection.Primitives": "(, 4.3.32767]", "System.Reflection.TypeExtensions": "(, 4.7.32767]", "System.Resources.Extensions": "(, 6.0.32767]", "System.Resources.Reader": "(, 4.3.32767]", "System.Resources.ResourceManager": "(, 4.3.32767]", "System.Resources.Writer": "(, 4.3.32767]", "System.Runtime": "(, 4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(, 6.0.32767]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.32767]", "System.Runtime.Extensions": "(, 4.3.32767]", "System.Runtime.Handles": "(, 4.3.32767]", "System.Runtime.InteropServices": "(, 4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.32767]", "System.Runtime.Loader": "(, 4.3.32767]", "System.Runtime.Numerics": "(, 4.3.32767]", "System.Runtime.Serialization.Formatters": "(, 4.3.32767]", "System.Runtime.Serialization.Json": "(, 4.3.32767]", "System.Runtime.Serialization.Primitives": "(, 4.3.32767]", "System.Runtime.Serialization.Xml": "(, 4.3.32767]", "System.Runtime.WindowsRuntime": "(, 4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.32767]", "System.Security.AccessControl": "(, 6.0.32767]", "System.Security.Claims": "(, 4.3.32767]", "System.Security.Cryptography.Algorithms": "(, 4.3.32767]", "System.Security.Cryptography.Cng": "(, 5.0.32767]", "System.Security.Cryptography.Csp": "(, 4.3.32767]", "System.Security.Cryptography.Encoding": "(, 4.3.32767]", "System.Security.Cryptography.OpenSsl": "(, 5.0.32767]", "System.Security.Cryptography.Pkcs": "(, 6.0.32767]", "System.Security.Cryptography.Primitives": "(, 4.3.32767]", "System.Security.Cryptography.ProtectedData": "(, 6.0.32767]", "System.Security.Cryptography.X509Certificates": "(, 4.3.32767]", "System.Security.Cryptography.Xml": "(, 6.0.32767]", "System.Security.Permissions": "(, 6.0.32767]", "System.Security.Principal": "(, 4.3.32767]", "System.Security.Principal.Windows": "(, 5.0.32767]", "System.Security.SecureString": "(, 4.3.32767]", "System.Text.Encoding": "(, 4.3.32767]", "System.Text.Encoding.CodePages": "(, 6.0.32767]", "System.Text.Encoding.Extensions": "(, 4.3.32767]", "System.Text.Encodings.Web": "(, 6.0.32767]", "System.Text.Json": "(, 6.0.32767]", "System.Text.RegularExpressions": "(, 4.3.32767]", "System.Threading": "(, 4.3.32767]", "System.Threading.AccessControl": "(, 6.0.32767]", "System.Threading.Channels": "(, 6.0.32767]", "System.Threading.Overlapped": "(, 4.3.32767]", "System.Threading.Tasks": "(, 4.3.32767]", "System.Threading.Tasks.Dataflow": "(, 6.0.32767]", "System.Threading.Tasks.Extensions": "(, 4.5.32767]", "System.Threading.Tasks.Parallel": "(, 4.3.32767]", "System.Threading.Thread": "(, 4.3.32767]", "System.Threading.ThreadPool": "(, 4.3.32767]", "System.Threading.Timer": "(, 4.3.32767]", "System.ValueTuple": "(, 4.5.32767]", "System.Windows.Extensions": "(, 6.0.32767]", "System.Xml.ReaderWriter": "(, 4.3.32767]", "System.Xml.XDocument": "(, 4.3.32767]", "System.Xml.XmlDocument": "(, 4.3.32767]", "System.Xml.XmlSerializer": "(, 4.3.32767]", "System.Xml.XPath": "(, 4.3.32767]", "System.Xml.XPath.XDocument": "(, 4.3.32767]"}}}}}