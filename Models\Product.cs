using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    public class Product : INotifyPropertyChanged
    {
        private int _id;
        private string _code;
        private string _name;
        private string _description;
        private int _categoryId;
        private string _categoryName;
        private decimal _purchasePrice;
        private decimal _sellingPrice;
        private int _quantity;
        private int _minQuantity;
        private DateTime _createdAt;
        private DateTime? _updatedAt;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CategoryId
        {
            get => _categoryId;
            set
            {
                if (_categoryId != value)
                {
                    _categoryId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CategoryName
        {
            get => _categoryName;
            set
            {
                if (_categoryName != value)
                {
                    _categoryName = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal PurchasePrice
        {
            get => _purchasePrice;
            set
            {
                if (_purchasePrice != value)
                {
                    _purchasePrice = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(Profit));
                    OnPropertyChanged(nameof(ProfitMargin));
                }
            }
        }

        public decimal SellingPrice
        {
            get => _sellingPrice;
            set
            {
                if (_sellingPrice != value)
                {
                    _sellingPrice = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(Profit));
                    OnPropertyChanged(nameof(ProfitMargin));
                }
            }
        }

        public int Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StockValue));
                    OnPropertyChanged(nameof(IsLowStock));
                }
            }
        }

        public int MinQuantity
        {
            get => _minQuantity;
            set
            {
                if (_minQuantity != value)
                {
                    _minQuantity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsLowStock));
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        // Calculated properties
        public decimal Profit => SellingPrice - PurchasePrice;

        public decimal ProfitMargin => PurchasePrice > 0 ? (Profit / PurchasePrice) * 100 : 0;

        public decimal StockValue => Quantity * PurchasePrice;

        public bool IsLowStock => Quantity <= MinQuantity;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ProductValidator : AbstractValidator<Product>
    {
        public ProductValidator()
        {
            RuleFor(p => p.Code).NotEmpty().WithMessage("Product code is required")
                .MaximumLength(20).WithMessage("Product code cannot exceed 20 characters");

            RuleFor(p => p.Name).NotEmpty().WithMessage("Product name is required")
                .MaximumLength(100).WithMessage("Product name cannot exceed 100 characters");

            RuleFor(p => p.Description).MaximumLength(500).WithMessage("Description cannot exceed 500 characters");

            RuleFor(p => p.CategoryId).NotEmpty().WithMessage("Category is required");

            RuleFor(p => p.PurchasePrice).GreaterThanOrEqualTo(0).WithMessage("Purchase price must be greater than or equal to 0");

            RuleFor(p => p.SellingPrice).GreaterThanOrEqualTo(0).WithMessage("Selling price must be greater than or equal to 0");

            RuleFor(p => p.Quantity).GreaterThanOrEqualTo(0).WithMessage("Quantity must be greater than or equal to 0");

            RuleFor(p => p.MinQuantity).GreaterThanOrEqualTo(0).WithMessage("Minimum quantity must be greater than or equal to 0");
        }
    }
}