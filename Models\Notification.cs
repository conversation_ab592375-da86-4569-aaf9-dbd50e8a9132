using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    public class Notification : INotifyPropertyChanged
    {
        private int _id;
        private string _title;
        private string _message;
        private string _type;
        private bool _isRead;
        private string _createdAt;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                if (_title != value)
                {
                    _title = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Message
        {
            get => _message;
            set
            {
                if (_message != value)
                {
                    _message = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Type
        {
            get => _type;
            set
            {
                if (_type != value)
                {
                    _type = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IconPath));
                }
            }
        }

        public bool IsRead
        {
            get => _isRead;
            set
            {
                if (_isRead != value)
                {
                    _isRead = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                    OnPropertyChanged(nameof(TimeAgo));
                }
            }
        }

        // Calculated properties
        public string FormattedCreatedAt
        {
            get
            {
                if (string.IsNullOrEmpty(CreatedAt))
                    return string.Empty;

                if (DateTime.TryParse(CreatedAt, out DateTime date))
                    return date.ToString("yyyy-MM-dd HH:mm:ss");

                return CreatedAt;
            }
        }

        public string TimeAgo
        {
            get
            {
                if (string.IsNullOrEmpty(CreatedAt) || !DateTime.TryParse(CreatedAt, out DateTime date))
                    return string.Empty;

                TimeSpan timeSince = DateTime.Now - date;

                if (timeSince.TotalSeconds < 60)
                    return "Just now";
                if (timeSince.TotalMinutes < 60)
                    return $"{(int)timeSince.TotalMinutes} minute{((int)timeSince.TotalMinutes == 1 ? "" : "s")} ago";
                if (timeSince.TotalHours < 24)
                    return $"{(int)timeSince.TotalHours} hour{((int)timeSince.TotalHours == 1 ? "" : "s")} ago";
                if (timeSince.TotalDays < 7)
                    return $"{(int)timeSince.TotalDays} day{((int)timeSince.TotalDays == 1 ? "" : "s")} ago";
                if (timeSince.TotalDays < 30)
                    return $"{(int)(timeSince.TotalDays / 7)} week{((int)(timeSince.TotalDays / 7) == 1 ? "" : "s")} ago";
                if (timeSince.TotalDays < 365)
                    return $"{(int)(timeSince.TotalDays / 30)} month{((int)(timeSince.TotalDays / 30) == 1 ? "" : "s")} ago";

                return $"{(int)(timeSince.TotalDays / 365)} year{((int)(timeSince.TotalDays / 365) == 1 ? "" : "s")} ago";
            }
        }

        public string IconPath
        {
            get
            {
                switch (Type?.ToLower())
                {
                    case "lowstock":
                    case "outofstock":
                        return "/Resources/Icons/inventory_icon.png";
                    case "paymentdue":
                    case "supplierpaymentdue":
                        return "/Resources/Icons/payment_icon.png";
                    case "backupsuccess":
                    case "backupfailure":
                        return "/Resources/Icons/backup_icon.png";
                    case "system":
                        return "/Resources/Icons/system_icon.png";
                    default:
                        return "/Resources/Icons/notification_icon.png";
                }
            }
        }

        public string TypeDisplayName
        {
            get
            {
                switch (Type?.ToLower())
                {
                    case "lowstock":
                        return "Low Stock";
                    case "outofstock":
                        return "Out of Stock";
                    case "paymentdue":
                        return "Payment Due";
                    case "supplierpaymentdue":
                        return "Supplier Payment";
                    case "backupsuccess":
                        return "Backup Success";
                    case "backupfailure":
                        return "Backup Failure";
                    case "system":
                        return "System";
                    default:
                        return Type ?? "Notification";
                }
            }
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName ?? string.Empty));
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName ?? string.Empty));
        }

        #endregion
    }

    public class NotificationValidator : AbstractValidator<Notification>
    {
        public NotificationValidator()
        {
            RuleFor(n => n.Title)
                .NotEmpty().WithMessage("Title is required")
                .MaximumLength(100).WithMessage("Title cannot exceed 100 characters");

            RuleFor(n => n.Message)
                .NotEmpty().WithMessage("Message is required")
                .MaximumLength(500).WithMessage("Message cannot exceed 500 characters");

            RuleFor(n => n.Type)
                .NotEmpty().WithMessage("Type is required")
                .MaximumLength(50).WithMessage("Type cannot exceed 50 characters");
        }
    }
}