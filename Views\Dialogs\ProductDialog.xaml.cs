using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views.Dialogs
{
    public partial class ProductDialog : Window
    {
        private readonly DatabaseService _dbService;
        private readonly ProductService _productService;
        private readonly Product? _existingProduct;
        private List<Category> _categories = new();

        public ProductDialog(Product? existingProduct = null)
        {
            InitializeComponent();
            
            _dbService = new DatabaseService();
            _productService = new ProductService(_dbService);
            _existingProduct = existingProduct;
            
            if (_existingProduct != null)
            {
                HeaderText.Text = "تعديل المنتج";
                Title = "تعديل المنتج";
            }
            
            Loaded += ProductDialog_Loaded;
            
            // Add event handlers for price calculation
            PurchasePriceTextBox.TextChanged += PriceTextBox_TextChanged;
            SellingPriceTextBox.TextChanged += PriceTextBox_TextChanged;
            QuantityTextBox.TextChanged += PriceTextBox_TextChanged;
        }

        private async void ProductDialog_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadCategoriesAsync();
            
            if (_existingProduct != null)
            {
                LoadProductData();
            }
            
            CalculateProfitMargin();
        }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                _categories = (await _productService.GetAllCategoriesAsync()).ToList();
                CategoryComboBox.ItemsSource = _categories;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadProductData()
        {
            if (_existingProduct == null) return;

            CodeTextBox.Text = _existingProduct.Code;
            NameTextBox.Text = _existingProduct.Name;
            DescriptionTextBox.Text = _existingProduct.Description;
            PurchasePriceTextBox.Text = _existingProduct.PurchasePrice.ToString("F2");
            SellingPriceTextBox.Text = _existingProduct.SellingPrice.ToString("F2");
            QuantityTextBox.Text = _existingProduct.Quantity.ToString();
            MinQuantityTextBox.Text = _existingProduct.MinQuantity.ToString();
            
            if (_existingProduct.CategoryId.HasValue)
            {
                CategoryComboBox.SelectedValue = _existingProduct.CategoryId.Value;
            }
        }

        private void PriceTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateProfitMargin();
        }

        private void CalculateProfitMargin()
        {
            if (decimal.TryParse(PurchasePriceTextBox.Text, out decimal purchasePrice) &&
                decimal.TryParse(SellingPriceTextBox.Text, out decimal sellingPrice) &&
                int.TryParse(QuantityTextBox.Text, out int quantity))
            {
                var profitMargin = sellingPrice - purchasePrice;
                var profitPercentage = purchasePrice > 0 ? (profitMargin / purchasePrice) * 100 : 0;
                var totalValue = sellingPrice * quantity;

                ProfitMarginText.Text = profitMargin.ToString("C");
                ProfitPercentageText.Text = profitPercentage.ToString("F1") + "%";
                TotalValueText.Text = totalValue.ToString("C");

                // Change color based on profit
                var brush = profitMargin >= 0 ? 
                    System.Windows.Media.Brushes.Green : 
                    System.Windows.Media.Brushes.Red;
                
                ProfitMarginText.Foreground = brush;
                ProfitPercentageText.Foreground = brush;
            }
            else
            {
                ProfitMarginText.Text = "0.00";
                ProfitPercentageText.Text = "0%";
                TotalValueText.Text = "0.00";
            }
        }

        private void NumericTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only numbers and decimal point
            var regex = new Regex(@"^[0-9]*\.?[0-9]*$");
            var textBox = sender as TextBox;
            var newText = textBox?.Text.Insert(textBox.SelectionStart, e.Text);
            
            e.Handled = !regex.IsMatch(newText ?? string.Empty);
        }

        private void IntegerTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only numbers
            var regex = new Regex(@"^[0-9]+$");
            e.Handled = !regex.IsMatch(e.Text);
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var product = CreateProductFromInput();
                
                if (_existingProduct == null)
                {
                    await _productService.AddProductAsync(product);
                    MessageBox.Show("تم إضافة المنتج بنجاح", "نجح", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    product.Id = _existingProduct.Id;
                    product.CreatedAt = _existingProduct.CreatedAt;
                    await _productService.UpdateProductAsync(product);
                    MessageBox.Show("تم تحديث المنتج بنجاح", "نجح", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المنتج: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(CodeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال كود المنتج", "خطأ في الإدخال", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                CodeTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المنتج", "خطأ في الإدخال", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(PurchasePriceTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال سعر شراء صحيح", "خطأ في الإدخال", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                PurchasePriceTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(SellingPriceTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال سعر بيع صحيح", "خطأ في الإدخال", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                SellingPriceTextBox.Focus();
                return false;
            }

            if (!int.TryParse(QuantityTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال كمية صحيحة", "خطأ في الإدخال", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                QuantityTextBox.Focus();
                return false;
            }

            if (!int.TryParse(MinQuantityTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال الحد الأدنى للكمية", "خطأ في الإدخال", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                MinQuantityTextBox.Focus();
                return false;
            }

            return true;
        }

        private Product CreateProductFromInput()
        {
            return new Product
            {
                Code = CodeTextBox.Text.Trim(),
                Name = NameTextBox.Text.Trim(),
                Description = DescriptionTextBox.Text.Trim(),
                CategoryId = CategoryComboBox.SelectedValue as int?,
                PurchasePrice = decimal.Parse(PurchasePriceTextBox.Text),
                SellingPrice = decimal.Parse(SellingPriceTextBox.Text),
                Quantity = int.Parse(QuantityTextBox.Text),
                MinQuantity = int.Parse(MinQuantityTextBox.Text),
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
