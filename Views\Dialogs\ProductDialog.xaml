<Window x:Class="SalesManagementSystem.Views.Dialogs.ProductDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="{DynamicResource AddProduct}"
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" x:Name="HeaderText" Text="{DynamicResource AddProduct}"
                  FontSize="24" FontWeight="Medium"
                  Margin="0,0,0,20" HorizontalAlignment="Center"/>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>

                <!-- Product Code -->
                <TextBox x:Name="CodeTextBox"
                        materialDesign:HintAssist.Hint="{DynamicResource ProductCode}"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        MaxLength="50"
                        Margin="0,0,0,15"/>

                <!-- Product Name -->
                <TextBox x:Name="NameTextBox"
                        materialDesign:HintAssist.Hint="{DynamicResource ProductName}"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        MaxLength="200"
                        Margin="0,0,0,15"/>

                <!-- Description -->
                <TextBox x:Name="DescriptionTextBox"
                        materialDesign:HintAssist.Hint="{DynamicResource ProductDescription}"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        AcceptsReturn="True"
                        TextWrapping="Wrap"
                        Height="80"
                        MaxLength="500"
                        Margin="0,0,0,15"/>

                <!-- Category -->
                <ComboBox x:Name="CategoryComboBox"
                         materialDesign:HintAssist.Hint="{DynamicResource ProductCategory}"
                         Style="{StaticResource MaterialDesignOutlinedComboBox}"
                         DisplayMemberPath="Name"
                         SelectedValuePath="Id"
                         Margin="0,0,0,15"/>

                <!-- Price Section -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Purchase Price -->
                    <TextBox x:Name="PurchasePriceTextBox" Grid.Column="0"
                            materialDesign:HintAssist.Hint="{DynamicResource PurchasePrice}"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            PreviewTextInput="NumericTextBox_PreviewTextInput"/>

                    <!-- Selling Price -->
                    <TextBox x:Name="SellingPriceTextBox" Grid.Column="2"
                            materialDesign:HintAssist.Hint="{DynamicResource SellingPrice}"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            PreviewTextInput="NumericTextBox_PreviewTextInput"/>
                </Grid>

                <!-- Quantity Section -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Current Quantity -->
                    <TextBox x:Name="QuantityTextBox" Grid.Column="0"
                            materialDesign:HintAssist.Hint="{DynamicResource Quantity}"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            PreviewTextInput="IntegerTextBox_PreviewTextInput"/>

                    <!-- Minimum Quantity -->
                    <TextBox x:Name="MinQuantityTextBox" Grid.Column="2"
                            materialDesign:HintAssist.Hint="{DynamicResource MinQuantity}"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            PreviewTextInput="IntegerTextBox_PreviewTextInput"/>
                </Grid>

                <!-- Profit Margin Display -->
                <materialDesign:Card Padding="15" Background="{DynamicResource MaterialDesignCardBackground}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="هامش الربح" FontSize="12"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="ProfitMarginText" Text="0.00" FontSize="16"
                                     FontWeight="Bold" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1">
                            <TextBlock Text="نسبة الربح" FontSize="12"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="ProfitPercentageText" Text="0%" FontSize="16"
                                     FontWeight="Bold" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="القيمة الإجمالية" FontSize="12"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalValueText" Text="0.00" FontSize="16"
                                     FontWeight="Bold" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal"
                   HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="SaveButton"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Content="{DynamicResource Save}"
                   Click="SaveButton_Click"
                   Margin="0,0,10,0"
                   MinWidth="100"/>

            <Button x:Name="CancelButton"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Content="{DynamicResource Cancel}"
                   Click="CancelButton_Click"
                   MinWidth="100"/>
        </StackPanel>
    </Grid>
</Window>
