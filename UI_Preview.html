<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المبيعات - معاينة الواجهة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #fafafa;
            direction: rtl;
        }
        
        .main-window {
            display: flex;
            height: 100vh;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            display: flex;
            flex-direction: column;
        }
        
        .app-header {
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .app-logo {
            width: 48px;
            height: 48px;
            background: white;
            border-radius: 50%;
            margin-left: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }
        
        .app-title {
            font-size: 20px;
            font-weight: bold;
        }
        
        .nav-menu {
            flex: 1;
            padding: 20px 0;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            margin: 5px 0;
            cursor: pointer;
            transition: background 0.3s;
            border: none;
            background: transparent;
            color: white;
            width: 100%;
            text-align: right;
        }
        
        .nav-item:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .nav-item.active {
            background: rgba(255,255,255,0.2);
            border-right: 4px solid white;
        }
        
        .nav-icon {
            width: 24px;
            height: 24px;
            margin-left: 10px;
            background: white;
            border-radius: 3px;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .top-bar {
            height: 60px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            padding: 0 20px;
            justify-content: space-between;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
        }
        
        .top-controls {
            display: flex;
            gap: 10px;
        }
        
        .control-btn {
            padding: 8px 16px;
            border: none;
            background: #2196F3;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .control-btn:hover {
            background: #1976D2;
        }
        
        .content-area {
            flex: 1;
            padding: 20px;
            background: #fafafa;
        }
        
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card-title {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .card-value {
            font-size: 28px;
            font-weight: bold;
            color: #2196F3;
        }
        
        .placeholder-content {
            background: white;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .placeholder-text {
            font-size: 24px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .placeholder-desc {
            color: #999;
        }
    </style>
</head>
<body>
    <div class="main-window">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="app-header">
                <div class="app-logo"></div>
                <div class="app-title">نظام إدارة المبيعات</div>
            </div>
            
            <div class="nav-menu">
                <button class="nav-item active">
                    <div class="nav-icon"></div>
                    لوحة التحكم
                </button>
                <button class="nav-item">
                    <div class="nav-icon"></div>
                    المنتجات
                </button>
                <button class="nav-item">
                    <div class="nav-icon"></div>
                    المبيعات
                </button>
                <button class="nav-item">
                    <div class="nav-icon"></div>
                    العملاء
                </button>
                <button class="nav-item">
                    <div class="nav-icon"></div>
                    الموردين
                </button>
                <button class="nav-item">
                    <div class="nav-icon"></div>
                    الموظفين
                </button>
                <button class="nav-item">
                    <div class="nav-icon"></div>
                    المصاريف
                </button>
                <button class="nav-item">
                    <div class="nav-icon"></div>
                    التقارير
                </button>
            </div>
            
            <button class="nav-item" style="margin-bottom: 10px;">
                <div class="nav-icon"></div>
                الإعدادات
            </button>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="top-bar">
                <div class="page-title">لوحة التحكم</div>
                <div class="top-controls">
                    <button class="control-btn">تغيير السمة</button>
                    <button class="control-btn">English</button>
                </div>
            </div>
            
            <div class="content-area">
                <div class="dashboard-cards">
                    <div class="card">
                        <div class="card-title">إجمالي المبيعات</div>
                        <div class="card-value">$0.00</div>
                    </div>
                    <div class="card">
                        <div class="card-title">إجمالي المصاريف</div>
                        <div class="card-value">$0.00</div>
                    </div>
                    <div class="card">
                        <div class="card-title">صافي الربح</div>
                        <div class="card-value">$0.00</div>
                    </div>
                    <div class="card">
                        <div class="card-title">عدد المنتجات</div>
                        <div class="card-value">0</div>
                    </div>
                </div>
                
                <div class="placeholder-content">
                    <div class="placeholder-text">مرحباً بك في نظام إدارة المبيعات</div>
                    <div class="placeholder-desc">النظام جاهز للاستخدام - يمكنك البدء بإضافة المنتجات والعملاء</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
