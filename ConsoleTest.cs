using System;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Services;

namespace SalesManagementSystem
{
    class ConsoleTest
    {
        static async Task Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("🚀 بدء اختبار نظام إدارة المبيعات...");

            try
            {
                // اختبار خدمة قاعدة البيانات
                Console.WriteLine("📊 اختبار خدمة قاعدة البيانات...");
                var dbService = new DatabaseService();
                Console.WriteLine("✅ تم إنشاء قاعدة البيانات بنجاح");

                // اختبار الحصول على إصدار قاعدة البيانات
                var version = await dbService.GetDatabaseVersionAsync();
                Console.WriteLine($"📋 إصدار قاعدة البيانات: {version}");

                // تخطي خدمة الإعدادات لأنها تحتاج WPF

                // اختبار خدمة المنتجات
                Console.WriteLine("📦 اختبار خدمة المنتجات...");
                var productService = new ProductService(dbService);
                var productCount = await productService.GetProductCountAsync();
                Console.WriteLine($"📊 عدد المنتجات: {productCount}");

                // اختبار خدمة العملاء
                Console.WriteLine("👥 اختبار خدمة العملاء...");
                var customerService = new CustomerService(dbService);
                var customerCount = await customerService.GetCustomerCountAsync();
                Console.WriteLine($"📊 عدد العملاء: {customerCount}");

                // اختبار خدمة الموظفين
                Console.WriteLine("👨‍💼 اختبار خدمة الموظفين...");
                var employeeService = new EmployeeService(dbService);
                var employeeCount = await employeeService.GetEmployeeCountAsync();
                Console.WriteLine($"📊 عدد الموظفين: {employeeCount}");

                // اختبار خدمة الموردين
                Console.WriteLine("🏭 اختبار خدمة الموردين...");
                var supplierService = new SupplierService(dbService);
                var suppliers = await supplierService.GetAllSuppliersAsync();
                var supplierCount = suppliers.Count();
                Console.WriteLine($"📊 عدد الموردين: {supplierCount}");

                // اختبار خدمة المبيعات
                Console.WriteLine("💰 اختبار خدمة المبيعات...");
                var saleService = new SaleService(dbService, productService, customerService);
                var totalSales = await saleService.GetTotalSalesAsync();
                Console.WriteLine($"📊 إجمالي المبيعات: {totalSales:C}");

                // اختبار خدمة المشتريات
                Console.WriteLine("🛒 اختبار خدمة المشتريات...");
                var purchaseService = new PurchaseService(dbService, productService, supplierService);
                var totalPurchases = await purchaseService.GetTotalPurchasesAsync();
                Console.WriteLine($"📊 إجمالي المشتريات: {totalPurchases:C}");

                Console.WriteLine("🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح.");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"📋 تفاصيل الخطأ: {ex.StackTrace}");
                return;
            }

            Console.WriteLine("✅ انتهى الاختبار بنجاح!");
            Console.WriteLine("اضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }
    }
}
