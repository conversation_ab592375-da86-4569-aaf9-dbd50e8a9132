using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.IO;
using System.Threading.Tasks;
using Dapper;
using System.Reflection;
using System.Linq;

namespace SalesManagementSystem.Services
{
    public class DatabaseService
    {
        private readonly string _dbPath;
        private readonly string _connectionString;

        public DatabaseService()
        {
            string appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SalesManagementSystem");

            // Create directory if it doesn't exist
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }

            _dbPath = Path.Combine(appDataPath, "SalesManagement.db");
            _connectionString = $"Data Source={_dbPath};Version=3;";

            // Initialize database if it doesn't exist
            if (!File.Exists(_dbPath))
            {
                InitializeDatabase();
            }
        }

        private void InitializeDatabase()
        {
            SQLiteConnection.CreateFile(_dbPath);

            using (var connection = new SQLiteConnection(_connectionString))
            {
                connection.Open();

                // Create tables
                ExecuteNonQuery(connection, CreateProductsTableSql);
                ExecuteNonQuery(connection, CreateCategoriesTableSql);
                ExecuteNonQuery(connection, CreateCustomersTableSql);
                ExecuteNonQuery(connection, CreateSuppliersTableSql);
                ExecuteNonQuery(connection, CreateEmployeesTableSql);
                ExecuteNonQuery(connection, CreateExpensesTableSql);
                ExecuteNonQuery(connection, CreateExpenseCategoriesTableSql);
                ExecuteNonQuery(connection, CreateSalesTableSql);
                ExecuteNonQuery(connection, CreateSaleItemsTableSql);
                ExecuteNonQuery(connection, CreatePurchasesTableSql);
                ExecuteNonQuery(connection, CreatePurchaseItemsTableSql);
                ExecuteNonQuery(connection, CreateUsersTableSql);
                ExecuteNonQuery(connection, CreateSettingsTableSql);

                // Insert default data
                InsertDefaultData(connection);
            }
        }

        private void ExecuteNonQuery(SQLiteConnection connection, string sql)
        {
            using (var command = new SQLiteCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private void InsertDefaultData(SQLiteConnection connection)
        {
            // Insert default product categories
            string[] defaultCategories = { "Electronics", "Clothing", "Food", "Beverages", "Stationery", "Other" };
            foreach (var category in defaultCategories)
            {
                ExecuteNonQuery(connection, $"INSERT INTO Categories (Name) VALUES ('{category}')");
            }

            // Insert default expense categories
            string[] defaultExpenseCategories = { "Rent", "Utilities", "Salaries", "Maintenance", "Office Supplies", "Marketing", "Other" };
            foreach (var category in defaultExpenseCategories)
            {
                ExecuteNonQuery(connection, $"INSERT INTO ExpenseCategories (Name) VALUES ('{category}')");
            }

            // Insert default admin user
            ExecuteNonQuery(connection, "INSERT INTO Users (Username, PasswordHash, FullName, Role, IsActive) VALUES ('admin', 'admin', 'Administrator', 'Admin', 1)");

            // Insert default settings
            ExecuteNonQuery(connection, "INSERT INTO Settings (Key, Value) VALUES ('CompanyName', 'Sales Management System')");
            ExecuteNonQuery(connection, "INSERT INTO Settings (Key, Value) VALUES ('Language', 'en-US')");
            ExecuteNonQuery(connection, "INSERT INTO Settings (Key, Value) VALUES ('Theme', 'Light')");
            ExecuteNonQuery(connection, "INSERT INTO Settings (Key, Value) VALUES ('Currency', '$')");
            ExecuteNonQuery(connection, "INSERT INTO Settings (Key, Value) VALUES ('TaxRate', '15')");
        }

        public async Task<T> QuerySingleAsync<T>(string sql, object? param = null)
        {
            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                return await db.QuerySingleAsync<T>(sql, param);
            }
        }

        public async Task<T> QuerySingleOrDefaultAsync<T>(string sql, object? param = null)
        {
            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                return await db.QuerySingleOrDefaultAsync<T>(sql, param);
            }
        }

        public async Task<IEnumerable<T>> QueryAsync<T>(string sql, object? param = null)
        {
            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                return await db.QueryAsync<T>(sql, param);
            }
        }

        public async Task<int> ExecuteAsync(string sql, object? param = null)
        {
            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                return await db.ExecuteAsync(sql, param);
            }
        }

        public async Task<T> InsertAsync<T>(string tableName, object entity)
        {
            var properties = entity.GetType().GetProperties()
                .Where(p => p.Name.ToLower() != "id" && p.GetValue(entity) != null)
                .ToList();

            var columnNames = string.Join(", ", properties.Select(p => p.Name));
            var paramNames = string.Join(", ", properties.Select(p => "@" + p.Name));

            var sql = $"INSERT INTO {tableName} ({columnNames}) VALUES ({paramNames}); SELECT last_insert_rowid()";

            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                var id = await db.ExecuteScalarAsync<long>(sql, entity);
                var idProperty = entity.GetType().GetProperty("Id");
                if (idProperty != null)
                {
                    idProperty.SetValue(entity, Convert.ChangeType(id, idProperty.PropertyType));
                }
                return (T)entity;
            }
        }

        public async Task<bool> UpdateAsync<T>(string tableName, T entity)
        {
            var properties = entity.GetType().GetProperties()
                .Where(p => p.Name.ToLower() != "id" && p.GetValue(entity) != null)
                .ToList();

            var setClause = string.Join(", ", properties.Select(p => $"{p.Name} = @{p.Name}"));
            var idProperty = entity.GetType().GetProperty("Id");
            var id = idProperty?.GetValue(entity);

            var sql = $"UPDATE {tableName} SET {setClause} WHERE Id = @Id";

            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                var result = await db.ExecuteAsync(sql, entity);
                return result > 0;
            }
        }

        public async Task<bool> DeleteAsync(string tableName, int id)
        {
            var sql = $"DELETE FROM {tableName} WHERE Id = @Id";

            using (IDbConnection db = new SQLiteConnection(_connectionString))
            {
                var result = await db.ExecuteAsync(sql, new { Id = id });
                return result > 0;
            }
        }

        #region SQL Create Table Statements

        private const string CreateProductsTableSql = @"
            CREATE TABLE Products (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Code TEXT NOT NULL,
                Name TEXT NOT NULL,
                Description TEXT,
                CategoryId INTEGER,
                PurchasePrice REAL NOT NULL,
                SellingPrice REAL NOT NULL,
                Quantity INTEGER NOT NULL,
                MinQuantity INTEGER NOT NULL,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
            );
            CREATE INDEX idx_products_code ON Products(Code);
            CREATE INDEX idx_products_category ON Products(CategoryId);
        ";

        private const string CreateCategoriesTableSql = @"
            CREATE TABLE Categories (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Name TEXT NOT NULL,
                Description TEXT
            );
        ";

        private const string CreateCustomersTableSql = @"
            CREATE TABLE Customers (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Name TEXT NOT NULL,
                Phone TEXT,
                Email TEXT,
                Address TEXT,
                Balance REAL NOT NULL DEFAULT 0,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT
            );
            CREATE INDEX idx_customers_name ON Customers(Name);
        ";

        private const string CreateSuppliersTableSql = @"
            CREATE TABLE Suppliers (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Name TEXT NOT NULL,
                Phone TEXT,
                Email TEXT,
                Address TEXT,
                Balance REAL NOT NULL DEFAULT 0,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT
            );
            CREATE INDEX idx_suppliers_name ON Suppliers(Name);
        ";

        private const string CreateEmployeesTableSql = @"
            CREATE TABLE Employees (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Name TEXT NOT NULL,
                Phone TEXT,
                Email TEXT,
                Address TEXT,
                Position TEXT NOT NULL,
                Salary REAL NOT NULL,
                HireDate TEXT NOT NULL,
                Status TEXT NOT NULL,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT
            );
            CREATE INDEX idx_employees_name ON Employees(Name);
        ";

        private const string CreateExpensesTableSql = @"
            CREATE TABLE Expenses (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Title TEXT NOT NULL,
                CategoryId INTEGER,
                Amount REAL NOT NULL,
                Date TEXT NOT NULL,
                Notes TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (CategoryId) REFERENCES ExpenseCategories(Id)
            );
            CREATE INDEX idx_expenses_category ON Expenses(CategoryId);
            CREATE INDEX idx_expenses_date ON Expenses(Date);
        ";

        private const string CreateExpenseCategoriesTableSql = @"
            CREATE TABLE ExpenseCategories (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Name TEXT NOT NULL,
                Description TEXT
            );
        ";

        private const string CreateSalesTableSql = @"
            CREATE TABLE Sales (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                InvoiceNumber TEXT NOT NULL,
                CustomerId INTEGER,
                Date TEXT NOT NULL,
                Subtotal REAL NOT NULL,
                Discount REAL NOT NULL DEFAULT 0,
                Tax REAL NOT NULL DEFAULT 0,
                Total REAL NOT NULL,
                PaymentMethod TEXT NOT NULL,
                PaymentStatus TEXT NOT NULL,
                Notes TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (CustomerId) REFERENCES Customers(Id)
            );
            CREATE INDEX idx_sales_invoice ON Sales(InvoiceNumber);
            CREATE INDEX idx_sales_customer ON Sales(CustomerId);
            CREATE INDEX idx_sales_date ON Sales(Date);
        ";

        private const string CreateSaleItemsTableSql = @"
            CREATE TABLE SaleItems (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                SaleId INTEGER NOT NULL,
                ProductId INTEGER NOT NULL,
                Quantity INTEGER NOT NULL,
                UnitPrice REAL NOT NULL,
                Discount REAL NOT NULL DEFAULT 0,
                Total REAL NOT NULL,
                FOREIGN KEY (SaleId) REFERENCES Sales(Id) ON DELETE CASCADE,
                FOREIGN KEY (ProductId) REFERENCES Products(Id)
            );
            CREATE INDEX idx_saleitems_sale ON SaleItems(SaleId);
            CREATE INDEX idx_saleitems_product ON SaleItems(ProductId);
        ";

        private const string CreatePurchasesTableSql = @"
            CREATE TABLE Purchases (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                InvoiceNumber TEXT NOT NULL,
                SupplierId INTEGER,
                Date TEXT NOT NULL,
                Subtotal REAL NOT NULL,
                Discount REAL NOT NULL DEFAULT 0,
                Tax REAL NOT NULL DEFAULT 0,
                Total REAL NOT NULL,
                PaymentMethod TEXT NOT NULL,
                PaymentStatus TEXT NOT NULL,
                Notes TEXT,
                CreatedAt TEXT NOT NULL,
                UpdatedAt TEXT,
                FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id)
            );
            CREATE INDEX idx_purchases_invoice ON Purchases(InvoiceNumber);
            CREATE INDEX idx_purchases_supplier ON Purchases(SupplierId);
            CREATE INDEX idx_purchases_date ON Purchases(Date);
        ";

        private const string CreatePurchaseItemsTableSql = @"
            CREATE TABLE PurchaseItems (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                PurchaseId INTEGER NOT NULL,
                ProductId INTEGER NOT NULL,
                Quantity INTEGER NOT NULL,
                UnitPrice REAL NOT NULL,
                Discount REAL NOT NULL DEFAULT 0,
                Total REAL NOT NULL,
                FOREIGN KEY (PurchaseId) REFERENCES Purchases(Id) ON DELETE CASCADE,
                FOREIGN KEY (ProductId) REFERENCES Products(Id)
            );
            CREATE INDEX idx_purchaseitems_purchase ON PurchaseItems(PurchaseId);
            CREATE INDEX idx_purchaseitems_product ON PurchaseItems(ProductId);
        ";

        private const string CreateUsersTableSql = @"
            CREATE TABLE Users (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Username TEXT NOT NULL,
                PasswordHash TEXT NOT NULL,
                FullName TEXT NOT NULL,
                Email TEXT,
                Role TEXT NOT NULL,
                IsActive INTEGER NOT NULL DEFAULT 1,
                LastLogin TEXT,
                CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                UpdatedAt TEXT
            );
            CREATE UNIQUE INDEX idx_users_username ON Users(Username);
        ";

        private const string CreateSettingsTableSql = @"
            CREATE TABLE Settings (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Key TEXT NOT NULL,
                Value TEXT,
                Description TEXT
            );
            CREATE UNIQUE INDEX idx_settings_key ON Settings(Key);
        ";

        #endregion
    }
}